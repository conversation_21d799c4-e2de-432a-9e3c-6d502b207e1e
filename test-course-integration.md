# Course Integration Test Plan

## Overview
This document outlines the testing plan for the Graphy API course integration feature.

## Prerequisites
1. Set up environment variables in `.env.local`:
   ```env
   GRAPHY_API_KEY=your-graphy-api-key-here
   GRAPHY_MID=your-graphy-mid-here
   GRAPHY_SUBDOMAIN=your-subdomain
   NEXT_PUBLIC_GRAPHY_SUBDOMAIN=your-subdomain
   ```

2. Run database migrations:
   ```bash
   npx prisma db push
   npx prisma generate
   ```

## Test Scenarios

### 1. Environment Configuration Test
- [ ] Verify all Graphy environment variables are set
- [ ] Test Graphy API connectivity
- [ ] Validate subdomain configuration

### 2. Database Models Test
- [ ] Verify Course model is created
- [ ] Verify CourseEnrollment model is created
- [ ] Test database relationships

### 3. API Endpoints Test

#### GET /api/courses
- [ ] Fetch courses without sync
- [ ] Fetch courses with sync=true
- [ ] Test pagination
- [ ] Test filtering by category
- [ ] Test search functionality
- [ ] Test price range filtering

#### GET /api/courses/categories
- [ ] Fetch course categories
- [ ] Verify category metadata

#### POST /api/courses/enroll
- [ ] Enroll in a course (creates Graphy learner)
- [ ] Test duplicate enrollment prevention
- [ ] Verify redirect URL generation

#### GET /api/courses/my-courses
- [ ] Fetch user's enrolled courses
- [ ] Test sync with Graphy enrollments
- [ ] Test status filtering
- [ ] Verify progress tracking

### 4. Frontend Components Test

#### Course Browse Page (/student/courses)
- [ ] Load and display courses
- [ ] Test course filtering
- [ ] Test search functionality
- [ ] Test pagination
- [ ] Test course enrollment flow
- [ ] Test sync functionality

#### My Courses Page (/student/my-courses)
- [ ] Display enrolled courses
- [ ] Show progress tracking
- [ ] Test status tabs (all, active, completed, paused)
- [ ] Test continue learning functionality
- [ ] Test sync functionality

#### Dashboard Widgets
- [ ] Enrolled Courses Widget displays correctly
- [ ] Course Stats Widget shows accurate data
- [ ] Recommended Courses Widget loads
- [ ] All widgets handle loading states
- [ ] Error states are handled gracefully

### 5. Navigation Test
- [ ] "Courses" link appears in student navigation
- [ ] "My Courses" link appears in student navigation
- [ ] Navigation links work correctly
- [ ] Active states are highlighted

### 6. Error Handling Test
- [ ] Network errors are handled gracefully
- [ ] API errors show appropriate messages
- [ ] Loading states are displayed
- [ ] Retry functionality works
- [ ] Offline state is handled

### 7. Integration Flow Test
1. **Complete User Journey:**
   - [ ] User logs in with Google
   - [ ] User browses courses
   - [ ] User filters/searches courses
   - [ ] User enrolls in a course
   - [ ] System creates Graphy learner account
   - [ ] User is redirected to Graphy course page
   - [ ] User completes payment on Graphy
   - [ ] User returns to platform
   - [ ] Enrolled course appears in "My Courses"
   - [ ] Progress is tracked and synced

### 8. Performance Test
- [ ] Course loading performance
- [ ] Image loading optimization
- [ ] API response times
- [ ] Database query performance

## Manual Testing Steps

### Step 1: Setup
1. Ensure environment variables are configured
2. Start the development server
3. Log in as a student user

### Step 2: Browse Courses
1. Navigate to `/student/courses`
2. Verify courses load from database
3. Click "Sync Courses" to fetch from Graphy
4. Test filtering and search
5. Test pagination

### Step 3: Enroll in Course
1. Select a course
2. Click "Enroll Now"
3. Verify Graphy learner creation
4. Verify redirect to Graphy course page
5. Complete enrollment on Graphy (if possible)

### Step 4: View My Courses
1. Navigate to `/student/my-courses`
2. Verify enrolled courses appear
3. Test progress sync
4. Test "Continue Learning" functionality

### Step 5: Dashboard Integration
1. Navigate to `/student` dashboard
2. Verify course widgets display
3. Test widget interactions

## Automated Testing

### API Tests
```javascript
// Test course fetching
describe('Course API', () => {
  test('GET /api/courses returns courses', async () => {
    const response = await fetch('/api/courses')
    expect(response.status).toBe(200)
    const data = await response.json()
    expect(data.courses).toBeDefined()
  })
  
  test('POST /api/courses/enroll creates enrollment', async () => {
    const response = await fetch('/api/courses/enroll', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ courseId: 'test-course-id' })
    })
    expect(response.status).toBe(200)
  })
})
```

### Component Tests
```javascript
// Test course components
describe('Course Components', () => {
  test('CourseCard renders correctly', () => {
    render(<CourseCard course={mockCourse} />)
    expect(screen.getByText(mockCourse.title)).toBeInTheDocument()
  })
  
  test('CourseGrid handles loading state', () => {
    render(<CourseGrid courses={[]} isLoading={true} />)
    expect(screen.getByTestId('course-skeleton')).toBeInTheDocument()
  })
})
```

## Success Criteria
- [ ] All API endpoints return expected data
- [ ] Course enrollment flow works end-to-end
- [ ] UI components render correctly
- [ ] Error handling is comprehensive
- [ ] Performance is acceptable
- [ ] User experience is smooth

## Known Issues & Limitations
1. Graphy API rate limits may affect sync frequency
2. Course images depend on Graphy CDN availability
3. Payment completion detection requires manual sync
4. Progress tracking depends on Graphy API data

## Next Steps
1. Implement automated testing suite
2. Add course favorites functionality
3. Implement course reviews and ratings
4. Add course completion certificates
5. Optimize performance with caching
6. Add real-time progress updates
