/**
 * Graphy API Configuration
 * 
 * This module provides configuration and utilities for integrating with Graphy API
 * for course management, user creation, and enrollment tracking.
 */

export interface GraphyConfig {
  apiKey: string
  mid: string
  subdomain: string
  baseUrl: string
}

export interface GraphyCourse {
  productId: string
  title: string
  price: number
  slug: string
  thumbnailImage?: string
  description?: string
  category?: string
  duration?: string
  instructor?: string
  rating?: number
  studentsCount?: number
  features?: string[]
}

export interface GraphyLearner {
  email: string
  name: string
  password?: string
  sendEmail?: boolean
}

export interface GraphyEnrollment {
  productId: string
  title: string
  enrolledAt: string
  progress?: number
  status?: 'active' | 'completed' | 'paused'
}

export interface GraphyLearnerResponse {
  email: string
  enrollments: GraphyEnrollment[]
}

/**
 * Get Graphy API configuration from environment variables
 */
export function getGraphyConfig(): GraphyConfig {
  const apiKey = "4936e29c-3b06-48bd-976b-c4a38bcdac09"
  const mid = "nextgenclasses"
  const subdomain = "nextgenclasses" // Using the same as MID

  if (!apiKey || !mid || !subdomain) {
    throw new Error(
      'Missing required Graphy configuration. Please set GRAPHY_API_KEY, GRAPHY_MID, and GRAPHY_SUBDOMAIN environment variables.'
    )
  }

  return {
    apiKey,
    mid,
    subdomain,
    baseUrl: 'https://api.ongraphy.com/public/v1'
  }
}

/**
 * Get Graphy course URL for redirecting users to purchase
 */
export function getGraphyCourseUrl(slug: string, subdomain?: string): string {
  const domain = subdomain || process.env.NEXT_PUBLIC_GRAPHY_SUBDOMAIN
  if (!domain) {
    throw new Error('Graphy subdomain not configured')
  }
  return `https://${domain}.graphy.com/courses/${slug}`
}

/**
 * Validate Graphy API response
 */
export function isGraphyApiError(response: any): boolean {
  return response && (response.error || response.status === 'error')
}

/**
 * Extract error message from Graphy API response
 */
export function getGraphyErrorMessage(response: any): string {
  if (response?.error) {
    return typeof response.error === 'string' ? response.error : 'Unknown API error'
  }
  if (response?.message) {
    return response.message
  }
  return 'Unknown error occurred'
}

/**
 * Build query string for Graphy API requests
 */
export function buildGraphyQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (typeof value === 'object') {
        searchParams.append(key, JSON.stringify(value))
      } else {
        searchParams.append(key, String(value))
      }
    }
  })
  
  return searchParams.toString()
}

/**
 * Default headers for Graphy API requests
 */
export function getGraphyHeaders(): Record<string, string> {
  return {
    'Content-Type': 'application/x-www-form-urlencoded',
    'Accept': 'application/json'
  }
}

/**
 * Transform Graphy course data to our internal format
 */
export function transformGraphyCourse(course: any): GraphyCourse {
  // Handle category - it might be an array or string
  let category: string | undefined
  if (Array.isArray(course.category)) {
    category = course.category.length > 0 ? course.category[0] : undefined
  } else if (typeof course.category === 'string') {
    category = course.category
  }

  // Generate unique slug if missing or empty
  let slug = course.slug
  if (!slug || slug.trim() === '') {
    // Create slug from title and productId to ensure uniqueness
    const title = course.title || 'untitled-course'
    const productId = course.productId || course.id || 'unknown'
    slug = `${title.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim()}-${productId.slice(-8)}` // Add last 8 chars of productId for uniqueness
  }

  return {
    productId: course.productId || course.id,
    title: course.title || 'Untitled Course',
    price: course.price || 0,
    slug: slug,
    thumbnailImage: course.thumbnailImage || course.image,
    description: course.description || course.shortDescription,
    category: category,
    duration: course.duration,
    instructor: course.instructor?.name || course.instructorName,
    rating: course.rating || course.averageRating,
    studentsCount: course.studentsCount || course.enrolledCount,
    features: course.features || []
  }
}

/**
 * Transform Graphy enrollment data to our internal format
 */
export function transformGraphyEnrollment(enrollment: any): GraphyEnrollment {
  return {
    productId: enrollment.productId || enrollment.id,
    title: enrollment.title || 'Unknown Course',
    enrolledAt: enrollment.enrolledAt || enrollment.createdAt || new Date().toISOString(),
    progress: enrollment.progress || 0,
    status: enrollment.status || 'active'
  }
}
