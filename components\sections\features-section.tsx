'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Brain,
  Users,
  Globe,
  BarChart3,
  Clock,
  Shield,
  Smartphone,
  Headphones
} from 'lucide-react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';
import { AnimatedCard, StaggeredCards } from '@/components/ui/animated-card';
import { cn } from '@/lib/utils';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { ContactModal } from '@/components/modals/contact-modal';

const features = [
  {
    icon: Brain,
    title: 'AI Progress Tracking',
    description: 'Advanced AI algorithms analyze your learning patterns and provide personalized recommendations for optimal progress.',
    color: 'from-violet-500 to-purple-600',
    stats: '95% accuracy'
  },
  {
    icon: Users,
    title: 'Expert Teachers',
    description: 'Learn from India\'s top educators with proven track records and years of teaching experience.',
    color: 'from-blue-500 to-cyan-600',
    stats: '50+ experts'
  },
  {
    icon: Globe,
    title: 'Bilingual Content',
    description: 'Access courses in both Hindi and English to learn in your preferred language for better understanding.',
    color: 'from-green-500 to-emerald-600',
    stats: '2 languages'
  },
  {
    icon: BarChart3,
    title: 'Mock Tests',
    description: 'Practice with unlimited mock tests that simulate real exam conditions and provide detailed analytics.',
    color: 'from-pink-500 to-rose-600',
    stats: '1000+ tests'
  },
  {
    icon: Clock,
    title: '24/7 Learning',
    description: 'Study anytime, anywhere with our flexible platform that adapts to your schedule and learning pace.',
    color: 'from-orange-500 to-amber-600',
    stats: 'Always available'
  },
  {
    icon: Shield,
    title: 'Guaranteed Results',
    description: 'Our proven methodology and comprehensive approach ensure you achieve your target scores.',
    color: 'from-indigo-500 to-blue-600',
    stats: '98% success rate'
  },
  {
    icon: Smartphone,
    title: 'Mobile Learning',
    description: 'Access all features on your mobile device with our responsive design and dedicated mobile app.',
    color: 'from-teal-500 to-cyan-600',
    stats: 'iOS & Android'
  },
  {
    icon: Headphones,
    title: 'Doubt Support',
    description: 'Get instant doubt resolution from expert teachers through live chat, video calls, and discussion forums.',
    color: 'from-red-500 to-pink-600',
    stats: 'Instant help'
  }
];

export function FeaturesSection() {
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);
  const { data: session } = useSession();
  const router = useRouter();

  const { elementRef, isVisible } = useIntersectionObserver({
    threshold: 0.2,
    freezeOnceVisible: true,
  });

  const handleGetStarted = () => {
    if (session) {
      // User is signed in, redirect to their portal based on role
      if (session.user.role === 'ADMIN') {
        router.push('/admin');
      } else {
        router.push('/student');
      }
    } else {
      // User is not signed in, redirect to sign in page
      router.push('/auth/signin');
    }
  };

  const handleScheduleDemo = () => {
    setIsContactModalOpen(true);
  };

  return (
    <section ref={elementRef} className="py-20 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 right-20 w-96 h-96 bg-violet-300 rounded-full mix-blend-multiply filter blur-3xl animate-pulse-slow" />
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-cyan-300 rounded-full mix-blend-multiply filter blur-3xl animate-pulse-slow" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isVisible ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full glass text-sm font-medium text-violet-700 dark:text-violet-300 mb-6"
          >
            <Brain className="w-4 h-4" />
            Why Choose Us
          </motion.div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Features That{' '}
            <span className="text-gradient bg-gradient-primary">Make Us Different</span>
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Discover the advanced features and methodologies that have helped millions of students 
            achieve their dreams and crack competitive exams.
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          <StaggeredCards
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            staggerDelay={0.1}
          >
            {features.map((feature, index) => (
              <AnimatedCard
                key={feature.title}
                variant="glass"
                hover="lift"
                className="p-6 text-center group relative overflow-hidden"
              >
                {/* Background Gradient */}
                <div className={cn(
                  'absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-5 transition-opacity duration-500',
                  feature.color
                )} />
                
                {/* Icon */}
                <div className={cn(
                  'w-14 h-14 mx-auto mb-4 rounded-2xl bg-gradient-to-br flex items-center justify-center group-hover:scale-110 transition-transform duration-300',
                  feature.color
                )}>
                  <feature.icon className="w-7 h-7 text-white" />
                </div>
                
                {/* Content */}
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-violet-600 dark:group-hover:text-violet-400 transition-colors">
                  {feature.title}
                </h3>
                
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 leading-relaxed">
                  {feature.description}
                </p>

                {/* Stats */}
                <div className="text-xs font-medium text-violet-600 dark:text-violet-400 bg-violet-50 dark:bg-violet-900/30 px-3 py-1 rounded-full inline-block">
                  {feature.stats}
                </div>
              </AnimatedCard>
            ))}
          </StaggeredCards>
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="glass rounded-3xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Ready to Experience the Difference?
            </h3>
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
              Join millions of students who have transformed their exam preparation with our innovative platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleGetStarted}
                className="px-8 py-4 bg-gradient-primary text-white rounded-2xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-300"
              >
                Get Started
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleScheduleDemo}
                className="px-8 py-4 border-2 border-violet-500 text-violet-500 rounded-2xl font-semibold hover:bg-violet-500 hover:text-white transition-all duration-300"
              >
                Schedule Demo
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Contact Modal */}
      <ContactModal
        isOpen={isContactModalOpen}
        onClose={() => setIsContactModalOpen(false)}
      />
    </section>
  );
}
