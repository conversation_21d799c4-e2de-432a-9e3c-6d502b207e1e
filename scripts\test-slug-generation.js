#!/usr/bin/env node

/**
 * Test script to verify the new slug generation format
 * Format: course-title-in-lowercase-with-hyphens-productid
 */

// Simulate the new slug generation function
function generateSlug(title, productId) {
  return `${title.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters except spaces
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .trim()}-${productId}`
}

// Test cases with various course titles
const testCases = [
  {
    title: "ANM NURSING BATCH 2025 For All Nursing Exam",
    productId: "558a70b9-1234-5678-9abc-def012345678"
  },
  {
    title: "Advanced JavaScript & React Development",
    productId: "abc123def456"
  },
  {
    title: "Python Programming: From Beginner to Expert!",
    productId: "python2024"
  },
  {
    title: "Data Science & Machine Learning Course",
    productId: "ds-ml-2024"
  },
  {
    title: "Web Development Bootcamp (Full Stack)",
    productId: "webdev-fullstack"
  },
  {
    title: "Digital Marketing Masterclass 2024",
    productId: "dm-master-2024"
  }
]

console.log('🧪 Testing New Slug Generation Format')
console.log('Format: course-title-in-lowercase-with-hyphens-productid')
console.log('=' .repeat(80))

testCases.forEach((testCase, index) => {
  const slug = generateSlug(testCase.title, testCase.productId)
  
  console.log(`\n📚 Test Case ${index + 1}:`)
  console.log(`   Title: "${testCase.title}"`)
  console.log(`   Product ID: "${testCase.productId}"`)
  console.log(`   Generated Slug: "${slug}"`)
  console.log(`   Length: ${slug.length} characters`)
  console.log(`   Graphy URL: https://nextgenclasses.graphy.com/courses/${slug}`)
})

console.log('\n' + '='.repeat(80))
console.log('✅ All slugs generated successfully!')
console.log('✅ Format: title-in-lowercase-with-hyphens-productid')
console.log('✅ Special characters removed, spaces converted to hyphens')
console.log('✅ Product ID appended at the end with hyphen separator')

// Test edge cases
console.log('\n🔍 Testing Edge Cases:')

const edgeCases = [
  { title: "", productId: "empty-title" },
  { title: "   Spaces   Around   ", productId: "spaces-test" },
  { title: "Special!@#$%^&*()Characters", productId: "special-chars" },
  { title: "Multiple---Hyphens", productId: "hyphen-test" },
  { title: "Numbers 123 and Symbols !@#", productId: "mixed-content" }
]

edgeCases.forEach((testCase, index) => {
  const slug = generateSlug(testCase.title || 'untitled-course', testCase.productId)
  console.log(`   Edge Case ${index + 1}: "${testCase.title}" → "${slug}"`)
})

console.log('\n🎯 Slug Generation Rules:')
console.log('1. Convert title to lowercase')
console.log('2. Remove special characters (keep only a-z, 0-9, spaces)')
console.log('3. Replace spaces with hyphens')
console.log('4. Remove multiple consecutive hyphens')
console.log('5. Remove leading/trailing hyphens')
console.log('6. Append hyphen + product ID at the end')
console.log('7. Result: clean, URL-friendly slug')
