import { NextRequest } from 'next/server'
import { APIResponse, createAPIHandler } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { fetchGraphyCourses } from '@/lib/graphy-api'

// POST /api/admin/resync-courses - Force resync all courses with correct slugs
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { }) => {
    try {
      console.log('🔄 Starting complete course resync...')
      
      // Fetch all courses from Graphy
      const graphyCourses = await fetchGraphyCourses({ limit: 200 })
      console.log(`📡 Fetched ${graphyCourses.length} courses from Graphy`)
      
      let updatedCount = 0
      let createdCount = 0
      let errorCount = 0
      const slugChanges: any[] = []
      
      for (const course of graphyCourses) {
        try {
          // Check if course exists in database
          const existingCourse = await prisma.course.findUnique({
            where: { productId: course.productId },
            select: { id: true, slug: true, title: true }
          })
          
          const isUpdate = !!existingCourse
          const slugChanged = existingCourse && existingCourse.slug !== course.slug
          
          if (slugChanged) {
            slugChanges.push({
              productId: course.productId,
              title: course.title,
              oldSlug: existingCourse.slug,
              newSlug: course.slug,
              oldLength: existingCourse.slug?.length || 0,
              newLength: course.slug?.length || 0
            })
          }
          
          // Upsert course with original slug
          await prisma.course.upsert({
            where: { productId: course.productId },
            update: {
              title: course.title,
              description: course.description || null,
              price: course.price,
              slug: course.slug, // Store original slug exactly as received
              thumbnailImage: course.thumbnailImage || null,
              category: course.category || null,
              duration: course.duration || null,
              instructor: course.instructor || null,
              rating: course.rating || null,
              studentsCount: course.studentsCount || null,
              features: course.features || [],
              updatedAt: new Date()
            },
            create: {
              productId: course.productId,
              title: course.title,
              description: course.description || null,
              price: course.price,
              slug: course.slug, // Store original slug exactly as received
              thumbnailImage: course.thumbnailImage || null,
              category: course.category || null,
              duration: course.duration || null,
              instructor: course.instructor || null,
              rating: course.rating || null,
              studentsCount: course.studentsCount || null,
              features: course.features || []
            }
          })
          
          if (isUpdate) {
            updatedCount++
          } else {
            createdCount++
          }
          
          console.log(`✅ ${isUpdate ? 'Updated' : 'Created'} course: ${course.title} (slug: ${course.slug})`)
          
        } catch (courseError: any) {
          console.error(`❌ Error processing course ${course.productId}:`, courseError.message)
          errorCount++
        }
      }
      
      console.log('🎉 Course resync completed!')
      
      return APIResponse.success({
        message: 'Course resync completed successfully',
        summary: {
          totalProcessed: graphyCourses.length,
          created: createdCount,
          updated: updatedCount,
          errors: errorCount,
          slugChanges: slugChanges.length
        },
        slugChanges: slugChanges.length > 0 ? slugChanges : undefined,
        details: {
          note: 'All courses now have original Graphy slugs preserved',
          timestamp: new Date().toISOString()
        }
      })
      
    } catch (error: any) {
      console.error('❌ Error during course resync:', error)
      return APIResponse.error(
        'Failed to resync courses: ' + error.message,
        500
      )
    }
  }
)

// GET /api/admin/resync-courses - Check resync status
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { }) => {
    try {
      // Get course count and sample slugs
      const totalCourses = await prisma.course.count()
      
      const sampleCourses = await prisma.course.findMany({
        take: 10,
        select: {
          productId: true,
          title: true,
          slug: true,
          updatedAt: true
        },
        orderBy: { updatedAt: 'desc' }
      })
      
      return APIResponse.success({
        message: 'Course resync status',
        summary: {
          totalCourses,
          lastUpdated: sampleCourses[0]?.updatedAt || null
        },
        sampleCourses: sampleCourses.map(c => ({
          productId: c.productId,
          title: c.title,
          slug: c.slug,
          slugLength: c.slug?.length || 0,
          lastUpdated: c.updatedAt
        }))
      })
      
    } catch (error: any) {
      console.error('❌ Error checking resync status:', error)
      return APIResponse.error(
        'Failed to check resync status: ' + error.message,
        500
      )
    }
  }
)
