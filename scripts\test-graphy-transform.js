#!/usr/bin/env node

/**
 * Test script to verify Graphy data transformation
 */

// Mock Graphy API response data
const mockGraphyResponse = {
  productId: "6847f7c100fab918558a70b9",
  title: "ANM NURSING BATCH 2025 For All Nursing Exam",
  description: undefined,
  price: 10000,
  slug: "",
  thumbnailImage: undefined,
  category: ["Health & Fitness"], // This is an array!
  duration: undefined,
  instructor: undefined,
  rating: undefined,
  studentsCount: undefined,
  features: []
}

// Simulate the transformation function
function transformGraphyCourse(course) {
  // Handle category - it might be an array or string
  let category
  if (Array.isArray(course.category)) {
    category = course.category.length > 0 ? course.category[0] : undefined
  } else if (typeof course.category === 'string') {
    category = course.category
  }

  // Generate unique slug if missing or empty
  let slug = course.slug
  if (!slug || slug.trim() === '') {
    // Create slug from title and productId to ensure uniqueness
    const title = course.title || 'untitled-course'
    const productId = course.productId || course.id || 'unknown'
    slug = `${title.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim()}-${productId.slice(-8)}` // Add last 8 chars of productId for uniqueness
  }

  return {
    productId: course.productId || course.id,
    title: course.title || 'Untitled Course',
    price: course.price || 0,
    slug: slug,
    thumbnailImage: course.thumbnailImage || null,
    description: course.description || null,
    category: category || null,
    duration: course.duration || null,
    instructor: course.instructor?.name || course.instructorName || null,
    rating: course.rating || course.averageRating || null,
    studentsCount: course.studentsCount || course.enrolledCount || null,
    features: course.features || []
  }
}

console.log('🧪 Testing Graphy Course Transformation...\n')

console.log('📥 Input (Mock Graphy Response):')
console.log(JSON.stringify(mockGraphyResponse, null, 2))

console.log('\n📤 Output (Transformed Course):')
const transformed = transformGraphyCourse(mockGraphyResponse)
console.log(JSON.stringify(transformed, null, 2))

console.log('\n✅ Transformation Results:')
console.log(`- Category converted from array to string: ${JSON.stringify(mockGraphyResponse.category)} → "${transformed.category}"`)
console.log(`- Undefined values converted to null: ${transformed.description === null ? '✅' : '❌'}`)
console.log(`- Empty slug generated unique slug: "${mockGraphyResponse.slug}" → "${transformed.slug}"`)
console.log(`- Features array preserved: ${Array.isArray(transformed.features) ? '✅' : '❌'}`)
console.log(`- Slug is unique and valid: ${transformed.slug.length > 0 && !transformed.slug.includes(' ') ? '✅' : '❌'}`)

console.log('\n🎯 Database Compatibility Check:')
const dbCompatible = {
  productId: typeof transformed.productId === 'string',
  title: typeof transformed.title === 'string',
  price: typeof transformed.price === 'number',
  slug: typeof transformed.slug === 'string',
  category: transformed.category === null || typeof transformed.category === 'string',
  description: transformed.description === null || typeof transformed.description === 'string',
  thumbnailImage: transformed.thumbnailImage === null || typeof transformed.thumbnailImage === 'string',
  duration: transformed.duration === null || typeof transformed.duration === 'string',
  instructor: transformed.instructor === null || typeof transformed.instructor === 'string',
  rating: transformed.rating === null || typeof transformed.rating === 'number',
  studentsCount: transformed.studentsCount === null || typeof transformed.studentsCount === 'number',
  features: Array.isArray(transformed.features)
}

const allCompatible = Object.values(dbCompatible).every(Boolean)

Object.entries(dbCompatible).forEach(([field, compatible]) => {
  console.log(`- ${field}: ${compatible ? '✅' : '❌'}`)
})

console.log(`\n${allCompatible ? '🎉' : '❌'} Overall compatibility: ${allCompatible ? 'PASS' : 'FAIL'}`)

if (allCompatible) {
  console.log('\n✨ The transformation function correctly handles Graphy API data!')
  console.log('   - Arrays are converted to strings')
  console.log('   - Undefined values become null')
  console.log('   - All types match Prisma schema expectations')
} else {
  console.log('\n⚠️  Some compatibility issues detected. Please review the transformation logic.')
}

console.log('\n📝 Note: This transformation will prevent Prisma validation errors.')
