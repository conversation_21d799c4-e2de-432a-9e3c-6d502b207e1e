import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { fetchGraphyCourses, GraphyApiError } from '@/lib/graphy-api'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  search: z.string().optional(),
  category: z.string().optional(),
  minPrice: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  maxPrice: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  sync: z.enum(['true', 'false']).optional().default('false')
})

// GET /api/courses - Get all courses with optional filtering
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const { 
        page = 1, 
        limit = 20, 
        search, 
        category, 
        minPrice, 
        maxPrice,
        sync 
      } = validatedQuery

      // If sync is requested, fetch fresh data from Graphy
      if (sync === 'true') {
        try {
          const graphyCourses = await fetchGraphyCourses({ limit: 100 })
          
          // Upsert courses in database
          for (const course of graphyCourses) {
            await prisma.course.upsert({
              where: { productId: course.productId },
              update: {
                title: course.title,
                description: course.description || null,
                price: course.price,
                slug: course.slug,
                thumbnailImage: course.thumbnailImage || null,
                category: course.category || null,
                duration: course.duration || null,
                instructor: course.instructor || null,
                rating: course.rating || null,
                studentsCount: course.studentsCount || null,
                features: course.features || [],
                updatedAt: new Date()
              },
              create: {
                productId: course.productId,
                title: course.title,
                description: course.description || null,
                price: course.price,
                slug: course.slug,
                thumbnailImage: course.thumbnailImage || null,
                category: course.category || null,
                duration: course.duration || null,
                instructor: course.instructor || null,
                rating: course.rating || null,
                studentsCount: course.studentsCount || null,
                features: course.features || []
              }
            })
          }
        } catch (error) {
          console.error('Failed to sync courses from Graphy:', error)
          // Continue with database query even if sync fails
        }
      }

      // Build where clause for filtering
      const where: any = {
        isActive: true
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { instructor: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (category) {
        where.category = { equals: category, mode: 'insensitive' }
      }

      if (minPrice !== undefined || maxPrice !== undefined) {
        where.price = {}
        if (minPrice !== undefined) where.price.gte = minPrice
        if (maxPrice !== undefined) where.price.lte = maxPrice
      }

      // Get total count for pagination
      const total = await prisma.course.count({ where })

      // Get courses with pagination
      const courses = await prisma.course.findMany({
        where,
        orderBy: [
          { rating: 'desc' },
          { studentsCount: 'desc' },
          { createdAt: 'desc' }
        ],
        skip: (page - 1) * limit,
        take: limit,
        include: {
          _count: {
            select: { enrollments: true }
          }
        }
      })

      // Check which courses the user is actively enrolled in (not pending)
      const userEnrollments = await prisma.courseEnrollment.findMany({
        where: {
          userId: user.id,
          courseId: { in: courses.map(c => c.id) },
          status: 'ACTIVE' // Only consider ACTIVE enrollments, not PENDING
        },
        select: { courseId: true }
      })

      const enrolledCourseIds = new Set(userEnrollments.map(e => e.courseId))

      // Add enrollment status to courses
      const coursesWithEnrollment = courses.map(course => ({
        ...course,
        isEnrolled: enrolledCourseIds.has(course.id),
        enrollmentCount: course._count.enrollments
      }))

      return APIResponse.success({
        courses: coursesWithEnrollment,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })
    } catch (error) {
      console.error('Error fetching courses:', error)

      if (error instanceof GraphyApiError) {
        return APIResponse.error(
          'Failed to fetch courses from Graphy: ' + error.message,
          500
        )
      }

      // Return empty result instead of error to prevent frontend crashes
      return APIResponse.success({
        courses: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        }
      })
    }
  }
)
