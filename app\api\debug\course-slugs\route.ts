import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { fetchGraphyCourses } from '@/lib/graphy-api'

// GET /api/debug/course-slugs - Debug course slug issues
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debugging course slugs...')
    
    // Fetch courses from Graphy API
    const graphyCourses = await fetchGraphyCourses({ limit: 5 })
    console.log('📡 Fetched courses from Graphy:', graphyCourses.length)
    
    // Fetch courses from database
    const dbCourses = await prisma.course.findMany({
      take: 5,
      select: {
        id: true,
        productId: true,
        title: true,
        slug: true,
        createdAt: true
      }
    })
    console.log('💾 Fetched courses from DB:', dbCourses.length)
    
    // Compare slugs
    const comparison = graphyCourses.map(graphyCourse => {
      const dbCourse = dbCourses.find(db => db.productId === graphyCourse.productId)
      
      return {
        productId: graphyCourse.productId,
        title: graphyCourse.title,
        graphy: {
          originalSlug: graphyCourse.slug,
          slugLength: graphyCourse.slug?.length || 0,
          slugType: typeof graphyCourse.slug
        },
        database: dbCourse ? {
          storedSlug: dbCourse.slug,
          slugLength: dbCourse.slug?.length || 0,
          slugType: typeof dbCourse.slug,
          matches: dbCourse.slug === graphyCourse.slug
        } : null,
        urls: {
          expectedUrl: `https://nextgenclasses.graphy.com/courses/${graphyCourse.slug}`,
          actualUrl: dbCourse ? `https://nextgenclasses.graphy.com/courses/${dbCourse.slug}` : 'Not in DB'
        }
      }
    })
    
    // Check for any slug issues
    const issues = comparison.filter(c => 
      !c.database?.matches || 
      !c.graphy.originalSlug || 
      c.graphy.slugLength === 0
    )
    
    return APIResponse.success({
      summary: {
        graphyCoursesCount: graphyCourses.length,
        dbCoursesCount: dbCourses.length,
        issuesFound: issues.length
      },
      comparison,
      issues,
      recommendations: issues.length > 0 ? [
        'Some courses have slug mismatches',
        'Consider re-syncing courses from Graphy',
        'Check if slug transformation is preserving original values'
      ] : [
        'All slugs appear to be correctly stored'
      ]
    })
    
  } catch (error: any) {
    console.error('❌ Error debugging course slugs:', error)
    return APIResponse.error('Failed to debug course slugs: ' + error.message, 500)
  }
}
