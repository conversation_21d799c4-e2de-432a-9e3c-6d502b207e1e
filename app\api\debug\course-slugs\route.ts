import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { fetchGraphyCourses } from '@/lib/graphy-api'

// GET /api/debug/course-slugs - Debug course slug issues
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debugging course slugs...')

    // Fetch raw courses from Graphy API (before transformation)
    const { getGraphyConfig } = await import('@/lib/graphy-config')
    const config = getGraphyConfig()

    const graphyResponse = await fetch(`${config.baseUrl}/products?mid=${config.mid}&key=${config.apiKey}&limit=5`)
    const rawGraphyData = await graphyResponse.json()
    const rawCourses = rawGraphyData.data || []

    console.log('📡 Raw Graphy courses:', rawCourses.length)

    // Fetch transformed courses
    const graphyCourses = await fetchGraphyCourses({ limit: 5 })
    console.log('🔄 Transformed courses:', graphyCourses.length)

    // Fetch courses from database
    const dbCourses = await prisma.course.findMany({
      take: 5,
      select: {
        id: true,
        productId: true,
        title: true,
        slug: true,
        createdAt: true
      }
    })
    console.log('💾 Fetched courses from DB:', dbCourses.length)
    
    // Compare slugs across all stages
    const comparison = rawCourses.slice(0, 5).map(rawCourse => {
      const transformedCourse = graphyCourses.find(gc => gc.productId === (rawCourse.productId || rawCourse.id))
      const dbCourse = dbCourses.find(db => db.productId === (rawCourse.productId || rawCourse.id))

      return {
        productId: rawCourse.productId || rawCourse.id,
        title: rawCourse.title,
        raw: {
          originalSlug: rawCourse.slug,
          slugLength: rawCourse.slug?.length || 0,
          slugType: typeof rawCourse.slug,
          hasSlug: !!rawCourse.slug
        },
        transformed: transformedCourse ? {
          transformedSlug: transformedCourse.slug,
          slugLength: transformedCourse.slug?.length || 0,
          slugType: typeof transformedCourse.slug,
          matchesRaw: transformedCourse.slug === rawCourse.slug
        } : null,
        database: dbCourse ? {
          storedSlug: dbCourse.slug,
          slugLength: dbCourse.slug?.length || 0,
          slugType: typeof dbCourse.slug,
          matchesRaw: dbCourse.slug === rawCourse.slug,
          matchesTransformed: transformedCourse ? dbCourse.slug === transformedCourse.slug : false
        } : null,
        urls: {
          expectedUrl: `https://nextgenclasses.graphy.com/courses/${rawCourse.slug}`,
          transformedUrl: transformedCourse ? `https://nextgenclasses.graphy.com/courses/${transformedCourse.slug}` : 'Not transformed',
          actualUrl: dbCourse ? `https://nextgenclasses.graphy.com/courses/${dbCourse.slug}` : 'Not in DB'
        }
      }
    })
    
    // Check for any slug issues
    const issues = comparison.filter(c =>
      !c.database?.matchesRaw ||
      !c.raw.originalSlug ||
      c.raw.slugLength === 0 ||
      !c.transformed?.matchesRaw
    )
    
    return APIResponse.success({
      summary: {
        rawCoursesCount: rawCourses.length,
        transformedCoursesCount: graphyCourses.length,
        dbCoursesCount: dbCourses.length,
        issuesFound: issues.length
      },
      comparison,
      issues,
      recommendations: issues.length > 0 ? [
        'Some courses have slug mismatches between raw Graphy data and stored data',
        'Check if transformation is modifying original slugs',
        'Consider re-syncing courses from Graphy with fixed transformation'
      ] : [
        'All slugs are correctly preserved from Graphy to database'
      ]
    })
    
  } catch (error: any) {
    console.error('❌ Error debugging course slugs:', error)
    return APIResponse.error('Failed to debug course slugs: ' + error.message, 500)
  }
}
