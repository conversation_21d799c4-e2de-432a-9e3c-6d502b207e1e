'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, RefreshCw, Clock, CheckCircle, XCircle, AlertCircle, CreditCard } from 'lucide-react';
import Link from 'next/link';

export default function RefundPolicyPage() {
  const refundScenarios = [
    {
      title: 'Eligible for Full Refund',
      icon: CheckCircle,
      color: 'from-green-500 to-emerald-600',
      scenarios: [
        'Request made within 7 days of purchase',
        'Less than 20% of course content accessed',
        'Technical issues preventing course access (after support attempts)',
        'Course significantly different from description',
        'Duplicate purchase made by mistake'
      ]
    },
    {
      title: 'Partial Refund Eligible',
      icon: AlertCircle,
      color: 'from-yellow-500 to-orange-600',
      scenarios: [
        'Request made within 14 days but 20-50% content accessed',
        'Course discontinued by ExamAce (prorated refund)',
        'Major technical issues affecting course quality',
        'Instructor-related issues (subject to review)'
      ]
    },
    {
      title: 'Not Eligible for Refund',
      icon: XCircle,
      color: 'from-red-500 to-pink-600',
      scenarios: [
        'Request made after 30 days of purchase',
        'More than 50% of course content accessed',
        'Course completed or certificate downloaded',
        'Violation of terms of service',
        'Free courses or promotional offers'
      ]
    }
  ];

  const refundProcess = [
    {
      step: 1,
      title: 'Submit Request',
      description: 'Contact our support team with your refund request and reason',
      icon: '📝'
    },
    {
      step: 2,
      title: 'Review Process',
      description: 'Our team reviews your request within 2-3 business days',
      icon: '🔍'
    },
    {
      step: 3,
      title: 'Decision Notification',
      description: 'You\'ll receive an email with our decision and next steps',
      icon: '📧'
    },
    {
      step: 4,
      title: 'Refund Processing',
      description: 'If approved, refund is processed within 5-7 business days',
      icon: '💳'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Home
          </Link>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="w-16 h-16 bg-gradient-to-r from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <RefreshCw className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-violet-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6">
            Refund Policy
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            We want you to be completely satisfied with your learning experience. Here's our comprehensive refund policy.
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
            Last updated: January 28, 2025
          </p>
        </motion.div>

        {/* Money-Back Guarantee */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl p-8 mb-8 text-white"
        >
          <div className="flex items-center gap-4 mb-4">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-white" />
            </div>
            <h2 className="text-2xl font-bold">7-Day Money-Back Guarantee</h2>
          </div>
          <p className="text-green-100 text-lg leading-relaxed">
            We're confident in the quality of our courses. If you're not completely satisfied with your purchase, 
            you can request a full refund within 7 days of enrollment, provided you've accessed less than 20% of the course content.
          </p>
        </motion.div>

        {/* Refund Eligibility */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Refund Eligibility
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {refundScenarios.map((scenario, index) => (
              <motion.div
                key={scenario.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700"
              >
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${scenario.color} flex items-center justify-center mb-4`}>
                  <scenario.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {scenario.title}
                </h3>
                <ul className="space-y-2">
                  {scenario.scenarios.map((item, itemIndex) => (
                    <li key={itemIndex} className="text-sm text-gray-600 dark:text-gray-300 flex items-start gap-2">
                      <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                      {item}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Refund Process */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white dark:bg-gray-800 rounded-2xl p-8 mb-8 border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center gap-4 mb-8">
            <div className="w-12 h-12 bg-gradient-to-r from-violet-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Refund Process</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {refundProcess.map((step, index) => (
              <div key={step.step} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 text-2xl">
                  {step.icon}
                </div>
                <div className="w-8 h-8 bg-violet-500 text-white rounded-full flex items-center justify-center mx-auto mb-3 text-sm font-bold">
                  {step.step}
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                  {step.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Important Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="space-y-8"
        >
          {/* Processing Times */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center">
                <CreditCard className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Processing Times</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Refund to Original Payment Method</h3>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li>• Credit/Debit Cards: 5-7 business days</li>
                  <li>• UPI/Digital Wallets: 2-3 business days</li>
                  <li>• Net Banking: 3-5 business days</li>
                  <li>• EMI Purchases: 7-14 business days</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Review Timeline</h3>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li>• Standard Requests: 2-3 business days</li>
                  <li>• Complex Cases: 5-7 business days</li>
                  <li>• Technical Issues: 3-5 business days</li>
                  <li>• Dispute Resolution: 7-14 business days</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Special Circumstances */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Special Circumstances</h2>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Course Cancellation by ExamAce</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  If we cancel a course due to insufficient enrollment or other reasons, all enrolled students will receive 
                  a full refund or the option to transfer to a similar course at no additional cost.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Technical Issues</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  If you experience persistent technical issues that prevent you from accessing course content, and our 
                  support team cannot resolve the issue within 48 hours, you may be eligible for a full refund regardless 
                  of the time elapsed since purchase.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Medical or Emergency Situations</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  We understand that unexpected circumstances can arise. If you have a medical emergency or other serious 
                  situation that prevents you from completing a course, please contact our support team to discuss your options.
                </p>
              </div>
            </div>
          </div>

          {/* How to Request */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">How to Request a Refund</h2>
            
            <div className="space-y-4">
              <p className="text-gray-600 dark:text-gray-300">
                To request a refund, please contact our support team with the following information:
              </p>
              
              <ul className="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-2 ml-4">
                <li>Your registered email address</li>
                <li>Course name and purchase date</li>
                <li>Reason for refund request</li>
                <li>Any relevant screenshots or documentation</li>
              </ul>
              
              <div className="bg-violet-50 dark:bg-violet-900/20 rounded-lg p-4 mt-6">
                <p className="text-violet-800 dark:text-violet-200 font-medium">
                  💡 Tip: Before requesting a refund, try reaching out to our support team for help with any issues you're experiencing. 
                  We're often able to resolve problems quickly and help you get the most out of your course.
                </p>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-gradient-to-r from-violet-500 to-purple-600 rounded-2xl p-8 text-white">
            <h2 className="text-2xl font-bold mb-4">Need Help with a Refund?</h2>
            <p className="text-violet-100 mb-6">
              Our support team is here to help you with any refund-related questions or requests.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="font-semibold mb-2">Contact Information</h3>
                <div className="space-y-1 text-violet-100">
                  <p>Email: <EMAIL></p>
                  <p>Phone: +91 98765 43210</p>
                  <p>Hours: Mon-Fri, 9 AM - 6 PM IST</p>
                </div>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Response Times</h3>
                <div className="space-y-1 text-violet-100">
                  <p>Email: Within 24 hours</p>
                  <p>Phone: Immediate assistance</p>
                  <p>Refund Processing: 2-7 business days</p>
                </div>
              </div>
            </div>
            
            <Link
              href="/contact"
              className="inline-flex items-center gap-2 bg-white text-violet-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Contact Support Team
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
