import { NextRequest } from 'next/server'

// Debug route to test course fetching step by step
export async function GET(request: NextRequest) {
  const steps: any[] = []
  
  try {
    steps.push({ step: 1, message: 'Starting debug process', timestamp: new Date().toISOString() })
    
    // Test 1: Check environment variables
    const apiKey = "4936e29c-3b06-48bd-976b-c4a38bcdac09"
    const mid = "nextgenclasses"
    
    steps.push({ 
      step: 2, 
      message: 'Environment variables', 
      data: { 
        hasApiKey: !!apiKey, 
        hasMid: !!mid,
        apiKeyLength: apiKey?.length,
        mid: mid
      } 
    })
    
    // Test 2: Try to fetch from Graphy API
    const graphyUrl = `https://api.ongraphy.com/public/v1/products?mid=${mid}&key=${apiKey}&limit=3`
    steps.push({ step: 3, message: 'Making request to Graphy', url: graphyUrl })
    
    const graphyResponse = await fetch(graphyUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      }
    })
    
    steps.push({ 
      step: 4, 
      message: 'Graphy API response', 
      status: graphyResponse.status,
      statusText: graphyResponse.statusText,
      headers: Object.fromEntries(graphyResponse.headers.entries())
    })
    
    if (!graphyResponse.ok) {
      const errorText = await graphyResponse.text()
      steps.push({ step: 5, message: 'Graphy API error', error: errorText })
      
      return Response.json({
        success: false,
        message: 'Graphy API request failed',
        steps
      }, { status: 500 })
    }
    
    const graphyData = await graphyResponse.json()
    steps.push({ 
      step: 5, 
      message: 'Graphy API data received', 
      dataType: typeof graphyData,
      hasData: !!graphyData.data,
      dataLength: graphyData.data?.length,
      sampleCourse: graphyData.data?.[0] ? {
        productId: graphyData.data[0].productId || graphyData.data[0].id,
        title: graphyData.data[0].title,
        price: graphyData.data[0].price,
        category: graphyData.data[0].category,
        slug: graphyData.data[0].slug
      } : null
    })
    
    // Test 3: Try transformation
    if (graphyData.data && graphyData.data.length > 0) {
      const sampleCourse = graphyData.data[0]
      
      // Transform the course
      let category: string | undefined
      if (Array.isArray(sampleCourse.category)) {
        category = sampleCourse.category.length > 0 ? sampleCourse.category[0] : undefined
      } else if (typeof sampleCourse.category === 'string') {
        category = sampleCourse.category
      }

      let slug = sampleCourse.slug
      if (!slug || slug.trim() === '') {
        const title = sampleCourse.title || 'untitled-course'
        const productId = sampleCourse.productId || sampleCourse.id || 'unknown'
        slug = `${title.toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim()}-${productId.slice(-8)}`
      }

      const transformed = {
        productId: sampleCourse.productId || sampleCourse.id,
        title: sampleCourse.title || 'Untitled Course',
        price: sampleCourse.price || 0,
        slug: slug,
        thumbnailImage: sampleCourse.thumbnailImage || sampleCourse.image || null,
        description: sampleCourse.description || sampleCourse.shortDescription || null,
        category: category || null,
        duration: sampleCourse.duration || null,
        instructor: sampleCourse.instructor?.name || sampleCourse.instructorName || null,
        rating: sampleCourse.rating || sampleCourse.averageRating || null,
        studentsCount: sampleCourse.studentsCount || sampleCourse.enrolledCount || null,
        features: sampleCourse.features || []
      }
      
      steps.push({ 
        step: 6, 
        message: 'Course transformation successful', 
        original: sampleCourse,
        transformed: transformed
      })
    }
    
    // Test 4: Try database connection
    try {
      const { prisma } = await import('@/lib/prisma')
      const courseCount = await prisma.course.count()
      steps.push({ 
        step: 7, 
        message: 'Database connection successful', 
        existingCourses: courseCount 
      })
    } catch (dbError: any) {
      steps.push({ 
        step: 7, 
        message: 'Database connection failed', 
        error: dbError.message 
      })
    }
    
    return Response.json({
      success: true,
      message: 'Debug completed successfully',
      steps,
      summary: {
        graphyApiWorking: graphyResponse.ok,
        coursesFound: graphyData.data?.length || 0,
        transformationWorking: true
      }
    })
    
  } catch (error: any) {
    steps.push({ 
      step: 'ERROR', 
      message: 'Unexpected error', 
      error: error.message,
      stack: error.stack 
    })
    
    return Response.json({
      success: false,
      message: 'Debug failed with error',
      error: error.message,
      steps
    }, { status: 500 })
  }
}
