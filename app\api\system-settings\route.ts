import { NextRequest, NextResponse } from 'next/server';
import { APIResponse } from '@/lib/api-middleware';
import { prisma } from '@/lib/prisma';

// Cache for system settings to avoid frequent database queries
let settingsCache: any = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Helper function to get system settings with caching
async function getCachedSystemSettings() {
  const now = Date.now();
  
  // Return cached data if it's still valid
  if (settingsCache && (now - cacheTimestamp) < CACHE_DURATION) {
    return settingsCache;
  }

  const defaultSettings = {
    companyName: 'ExamAce',
    companyDescription: 'India\'s leading online coaching platform',
    companyMission: 'Empowering students to achieve their dreams through quality education',
    companyVision: 'To be the most trusted educational platform in India',
    contactEmail: '<EMAIL>',
    contactPhone: '+91 98765 43210',
    contactAddress: 'New Delhi, India',
    supportEmail: '<EMAIL>',
    privacyEmail: '<EMAIL>',
    legalEmail: '<EMAIL>',
    youtubeChannel: 'https://www.youtube.com/@YourChannelName',
    facebookPage: '',
    twitterHandle: '',
    instagramHandle: '',
    linkedinPage: '',
    logoUrl: '',
    faviconUrl: '',
    brandColor: '#8B5CF6',
    siteUrl: 'http://localhost:3000',
    timezone: 'Asia/Kolkata',
    language: 'en',
    businessHours: 'Mon-Fri, 9 AM - 6 PM IST',
    teamMembers: [
      {
        id: '1',
        name: 'Rajesh Kumar',
        title: 'Founder & CEO',
        bio: 'Former IIT graduate with 15+ years in education technology. Passionate about making quality education accessible to every student in India.',
        image: '/images/team/founder.jpg',
        expertise: ['Education Technology', 'Strategic Planning', 'Team Leadership']
      },
      {
        id: '2',
        name: 'Priya Sharma',
        title: 'Co-Founder & CTO',
        bio: 'Ex-Google engineer with expertise in AI and machine learning. Leading the technical innovation at ExamAce to create personalized learning experiences.',
        image: '/images/team/cofounder.jpg',
        expertise: ['Artificial Intelligence', 'Software Architecture', 'Product Development']
      }
    ],
    totalStudents: 500000,
    successRate: 98,
    coursesOffered: 50
  };

  try {
    // Get settings from database
    const settings = await prisma.systemSetting.findMany({
      where: {
        category: 'company'
      }
    });

    if (settings.length === 0) {
      // Create default settings if none exist
      const settingsToCreate = Object.entries(defaultSettings).map(([key, value]) => ({
        key: `company.${key}`,
        value: typeof value === 'object' ? JSON.stringify(value) : String(value),
        category: 'company',
        description: `Company ${key}`
      }));

      await prisma.systemSetting.createMany({
        data: settingsToCreate
      });

      settingsCache = defaultSettings;
    } else {
      // Convert key-value pairs back to object
      const result: any = {};
      settings.forEach(setting => {
        const key = setting.key.replace('company.', '');
        try {
          // Parse JSON for complex fields like teamMembers
          if (key === 'teamMembers' || key === 'totalStudents' || key === 'successRate' || key === 'coursesOffered') {
            result[key] = key === 'teamMembers' ? JSON.parse(setting.value) : parseInt(setting.value);
          } else {
            result[key] = setting.value;
          }
        } catch {
          result[key] = setting.value;
        }
      });

      settingsCache = { ...defaultSettings, ...result };
    }

    cacheTimestamp = now;
    return settingsCache;
  } catch (error) {
    console.error('Error fetching system settings:', error);
    // Return default settings if database query fails
    settingsCache = defaultSettings;
    cacheTimestamp = now;
    return defaultSettings;
  }
}

// GET /api/system-settings - Get public system settings
export async function GET(request: NextRequest) {
  try {
    const settings = await getCachedSystemSettings();
    
    // Return only public settings (exclude sensitive information)
    const publicSettings = {
      companyName: settings.companyName,
      companyDescription: settings.companyDescription,
      companyMission: settings.companyMission,
      companyVision: settings.companyVision,
      contactEmail: settings.contactEmail,
      contactPhone: settings.contactPhone,
      contactAddress: settings.contactAddress,
      supportEmail: settings.supportEmail,
      youtubeChannel: settings.youtubeChannel,
      facebookPage: settings.facebookPage,
      twitterHandle: settings.twitterHandle,
      instagramHandle: settings.instagramHandle,
      linkedinPage: settings.linkedinPage,
      logoUrl: settings.logoUrl,
      faviconUrl: settings.faviconUrl,
      brandColor: settings.brandColor,
      businessHours: settings.businessHours,
      teamMembers: settings.teamMembers,
      totalStudents: settings.totalStudents,
      successRate: settings.successRate,
      coursesOffered: settings.coursesOffered
    };

    return APIResponse.success({
      settings: publicSettings,
      message: 'System settings retrieved successfully'
    });
  } catch (error: any) {
    console.error('Error fetching public system settings:', error);
    return APIResponse.error('Failed to fetch system settings: ' + error.message, 500);
  }
}

// POST /api/system-settings/refresh-cache - Refresh settings cache
export async function POST(request: NextRequest) {
  try {
    // Clear cache to force refresh
    settingsCache = null;
    cacheTimestamp = 0;
    
    // Get fresh settings
    const settings = await getCachedSystemSettings();
    
    return APIResponse.success({
      message: 'Settings cache refreshed successfully',
      settings
    });
  } catch (error: any) {
    console.error('Error refreshing settings cache:', error);
    return APIResponse.error('Failed to refresh settings cache: ' + error.message, 500);
  }
}
