#!/usr/bin/env node

/**
 * Test script to verify Graphy API connectivity
 */

const https = require('https')

// Graphy API configuration (from your hardcoded values)
const GRAPHY_API_KEY = "4936e29c-3b06-48bd-976b-c4a38bcdac09"
const GRAPHY_MID = "nextgenclasses"
const GRAPHY_BASE_URL = "https://api.ongraphy.com/public/v1"

function testGraphyAPI() {
  console.log('🧪 Testing Graphy API Connectivity...\n')
  
  const params = new URLSearchParams({
    mid: GRAPHY_MID,
    key: GRAPHY_API_KEY,
    limit: '5'
  })
  
  const url = `${GRAPHY_BASE_URL}/products?${params}`
  
  console.log('📡 Making request to:', url)
  console.log('🔑 Using API Key:', GRAPHY_API_KEY.substring(0, 8) + '...')
  console.log('🏢 Using MID:', GRAPHY_MID)
  console.log('')
  
  https.get(url, (res) => {
    let data = ''
    
    console.log('📊 Response Status:', res.statusCode)
    console.log('📋 Response Headers:', JSON.stringify(res.headers, null, 2))
    console.log('')
    
    res.on('data', (chunk) => {
      data += chunk
    })
    
    res.on('end', () => {
      try {
        const jsonData = JSON.parse(data)
        
        console.log('✅ Response received successfully!')
        console.log('📦 Response Data:')
        console.log(JSON.stringify(jsonData, null, 2))
        
        if (jsonData.data && Array.isArray(jsonData.data)) {
          console.log(`\n🎯 Found ${jsonData.data.length} courses`)
          
          if (jsonData.data.length > 0) {
            console.log('\n📚 Sample Course:')
            const sampleCourse = jsonData.data[0]
            console.log('- Product ID:', sampleCourse.productId || sampleCourse.id)
            console.log('- Title:', sampleCourse.title)
            console.log('- Price:', sampleCourse.price)
            console.log('- Category:', sampleCourse.category)
            console.log('- Slug:', sampleCourse.slug)
            console.log('- Description:', sampleCourse.description?.substring(0, 100) + '...')
          }
        } else {
          console.log('⚠️  No courses data found in response')
        }
        
      } catch (error) {
        console.error('❌ Failed to parse JSON response:')
        console.error(error.message)
        console.log('\n📄 Raw Response:')
        console.log(data.substring(0, 500) + (data.length > 500 ? '...' : ''))
      }
    })
    
  }).on('error', (error) => {
    console.error('❌ Request failed:')
    console.error(error.message)
    
    if (error.code === 'ENOTFOUND') {
      console.log('\n💡 This might be a DNS/network issue')
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Connection refused - check if the API endpoint is correct')
    }
  })
}

// Test the API
testGraphyAPI()

// Also test the transformation
console.log('\n' + '='.repeat(60))
console.log('🔄 Testing Course Transformation...\n')

// Mock response based on what we might get
const mockCourse = {
  productId: "test123",
  title: "Test Course",
  price: 1000,
  slug: "",
  category: ["Technology"],
  description: undefined,
  features: []
}

function transformGraphyCourse(course) {
  let category
  if (Array.isArray(course.category)) {
    category = course.category.length > 0 ? course.category[0] : undefined
  } else if (typeof course.category === 'string') {
    category = course.category
  }

  let slug = course.slug
  if (!slug || slug.trim() === '') {
    const title = course.title || 'untitled-course'
    const productId = course.productId || course.id || 'unknown'
    slug = `${title.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()}-${productId.slice(-8)}`
  }

  return {
    productId: course.productId || course.id,
    title: course.title || 'Untitled Course',
    price: course.price || 0,
    slug: slug,
    thumbnailImage: course.thumbnailImage || course.image || null,
    description: course.description || course.shortDescription || null,
    category: category || null,
    duration: course.duration || null,
    instructor: course.instructor?.name || course.instructorName || null,
    rating: course.rating || course.averageRating || null,
    studentsCount: course.studentsCount || course.enrolledCount || null,
    features: course.features || []
  }
}

const transformed = transformGraphyCourse(mockCourse)
console.log('📥 Mock Input:', JSON.stringify(mockCourse, null, 2))
console.log('\n📤 Transformed Output:', JSON.stringify(transformed, null, 2))
console.log('\n✅ Transformation working correctly!')
