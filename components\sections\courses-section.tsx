'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calculator,
  Stethoscope,
  Building,
  GraduationCap,
  Users,
  Clock,
  Star,
  ArrowRight,
  Filter,
  BookOpen,
  ExternalLink
} from 'lucide-react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';
import { AnimatedCard } from '@/components/ui/animated-card';
import { AnimatedButton } from '@/components/ui/animated-button';
import { cn } from '@/lib/utils';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';

interface Course {
  id: string;
  productId: string;
  title: string;
  description?: string;
  category?: string;
  price: number;
  originalPrice?: number;
  duration?: string;
  studentsCount?: number;
  rating?: number;
  thumbnailImage?: string;
  features: string[];
  instructor?: string;
  slug: string;
  isEnrolled?: boolean;
}

// Static fallback courses for when API is not available
const fallbackCourses: Course[] = [
  {
    id: '1',
    productId: 'fallback-1',
    title: 'JEE Main & Advanced Complete Course',
    description: 'Comprehensive preparation for JEE with expert faculty and AI-powered practice',
    category: 'engineering',
    price: 99,
    originalPrice: 999,
    duration: '12 months',
    studentsCount: 50000,
    rating: 4.9,
    thumbnailImage: '/api/placeholder/400/250',
    features: ['Live Classes', 'Mock Tests', 'Doubt Solving', 'Study Material'],
    instructor: 'Expert Faculty',
    slug: 'jee-main-advanced-complete-course'
  },
  {
    id: '2',
    productId: 'fallback-2',
    title: 'NEET Complete Preparation',
    description: 'Master Biology, Chemistry, and Physics for NEET with proven strategies',
    category: 'medical',
    price: 99,
    originalPrice: 899,
    duration: '10 months',
    studentsCount: 75000,
    rating: 4.8,
    thumbnailImage: '/api/placeholder/400/250',
    features: ['Expert Faculty', 'Previous Year Papers', 'Biology Focus', 'Chemistry Labs'],
    instructor: 'Medical Experts',
    slug: 'neet-complete-preparation'
  },
  {
    id: '3',
    productId: 'fallback-3',
    title: 'UPSC Civil Services Complete',
    description: 'Comprehensive UPSC preparation with current affairs and answer writing',
    category: 'government',
    price: 149,
    originalPrice: 1299,
    duration: '18 months',
    studentsCount: 30000,
    rating: 4.9,
    thumbnailImage: '/api/placeholder/400/250',
    features: ['Current Affairs', 'Answer Writing', 'Interview Prep', 'Optional Subjects'],
    instructor: 'IAS Officers',
    slug: 'upsc-civil-services-complete'
  }
];

const categories = [
  { id: 'all', label: 'All Courses', icon: GraduationCap, color: 'violet' },
  { id: 'engineering', label: 'Engineering', icon: Calculator, color: 'blue' },
  { id: 'medical', label: 'Medical', icon: Stethoscope, color: 'pink' },
  { id: 'government', label: 'Government', icon: Building, color: 'green' },
  { id: 'teaching', label: 'Teaching', icon: Users, color: 'orange' },
];

export function CoursesSection() {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [courses, setCourses] = useState<Course[]>(fallbackCourses);
  const [isLoading, setIsLoading] = useState(false);
  const { data: session } = useSession();
  const { elementRef, isVisible } = useIntersectionObserver({
    threshold: 0.2,
    freezeOnceVisible: true,
  });

  // Fetch courses from public API (no auth required)
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/public/courses?limit=6&sync=true');

        if (response.ok) {
          const data = await response.json();
          if (data.courses && data.courses.length > 0) {
            setCourses(data.courses);
          }
        }
      } catch (error) {
        console.error('Error fetching courses:', error);
        // Keep fallback courses on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourses();
  }, []);

  const filteredCourses = activeCategory === 'all'
    ? courses
    : courses.filter(course => course.category === activeCategory);

  const handleEnrollClick = (course: Course) => {
    if (session) {
      // Redirect to student courses page for enrolled users
      window.location.href = '/student/courses';
    } else {
      // Redirect to sign-in for non-authenticated users
      window.location.href = '/auth/signin';
    }
  };

  const handleViewCourse = (course: Course) => {
    if (course.slug && process.env.NEXT_PUBLIC_GRAPHY_SUBDOMAIN) {
      // Open Graphy course page in new tab
      const graphyUrl = `https://${process.env.NEXT_PUBLIC_GRAPHY_SUBDOMAIN}.graphy.com/courses/${course.slug}`;
      window.open(graphyUrl, '_blank');
    } else {
      toast.info('Course details will be available soon!');
    }
  };

  return (
    <section ref={elementRef} className="py-20 bg-gradient-to-b from-violet-50 to-white dark:from-gray-800 dark:to-gray-900 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isVisible ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full glass text-sm font-medium text-violet-700 dark:text-violet-300 mb-6"
          >
            <Filter className="w-4 h-4" />
            Choose Your Path
          </motion.div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Explore Our{' '}
            <span className="text-gradient bg-gradient-primary">Courses</span>
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Comprehensive courses designed by experts to help you crack any Indian competitive exam
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={isVisible ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
              onClick={() => setActiveCategory(category.id)}
              className={cn(
                'flex items-center gap-2 px-6 py-3 rounded-2xl font-medium transition-all duration-300',
                activeCategory === category.id
                  ? 'bg-gradient-primary text-white shadow-glow'
                  : 'glass hover:shadow-lg text-gray-700 dark:text-gray-300'
              )}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <category.icon className="w-5 h-5" />
              {category.label}
            </motion.button>
          ))}
        </motion.div>

        {/* Courses Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {isLoading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <motion.div
                  key={`skeleton-${index}`}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="group"
                >
                  <div className="h-full overflow-hidden p-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-gray-800/20 shadow-xl">
                    <div className="h-48 bg-gray-200 dark:bg-gray-700 animate-pulse" />
                    <div className="p-6 space-y-4">
                      <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" />
                      <div className="flex gap-2">
                        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse" />
                        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse" />
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse" />
                        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse" />
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))
            ) : filteredCourses.length > 0 ? (
              filteredCourses.map((course, index) => (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="group"
              >
                <AnimatedCard
                  variant="glass"
                  hover="lift"
                  className="h-full overflow-hidden p-0"
                >
                  {/* Course Image */}
                  <div className="relative h-48 bg-gradient-to-br from-violet-400 to-purple-600 overflow-hidden">
                    {course.thumbnailImage ? (
                      <img
                        src={course.thumbnailImage}
                        alt={course.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Fallback to gradient background if image fails to load
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <BookOpen className="h-16 w-16 text-white/80" />
                      </div>
                    )}
                    <div className="absolute inset-0 bg-black/20" />
                    <div className="absolute top-4 left-4 flex gap-2">
                      {course.category && (
                        <span className="px-2 py-1 text-xs font-medium bg-white/20 backdrop-blur-sm rounded-lg text-white capitalize">
                          {course.category}
                        </span>
                      )}
                    </div>
                    {course.rating && (
                      <div className="absolute top-4 right-4 flex items-center gap-1 px-2 py-1 bg-white/20 backdrop-blur-sm rounded-lg text-white text-sm">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        {course.rating}
                      </div>
                    )}
                  </div>

                  {/* Course Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-violet-600 transition-colors">
                      {course.title}
                    </h3>
                    
                    <p className="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                      {course.description}
                    </p>

                    {/* Features */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {course.features.slice(0, 3).map((feature) => (
                        <span
                          key={feature}
                          className="px-2 py-1 text-xs bg-violet-100 dark:bg-violet-900 text-violet-700 dark:text-violet-300 rounded-lg"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>

                    {/* Course Stats */}
                    <div className="flex items-center gap-4 mb-4 text-sm text-gray-600 dark:text-gray-400">
                      {course.duration && (
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {course.duration}
                        </div>
                      )}
                      {course.studentsCount && (
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          {course.studentsCount.toLocaleString()}+
                        </div>
                      )}
                      {course.instructor && (
                        <div className="flex items-center gap-1">
                          <GraduationCap className="w-4 h-4" />
                          {course.instructor}
                        </div>
                      )}
                    </div>

                    {/* Price and CTA */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold text-gray-900 dark:text-white">
                          ₹{course.price}
                        </span>
                        {course.originalPrice && course.originalPrice > course.price && (
                          <span className="text-sm text-gray-500 line-through">
                            ₹{course.originalPrice}
                          </span>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <AnimatedButton
                          variant="outline"
                          size="sm"
                          className="group"
                          onClick={() => handleViewCourse(course)}
                        >
                          <ExternalLink className="w-4 h-4" />
                        </AnimatedButton>
                        <AnimatedButton
                          variant="primary"
                          size="sm"
                          className="group"
                          onClick={() => handleEnrollClick(course)}
                        >
                          {session ? 'Enroll' : 'Sign In'}
                          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                        </AnimatedButton>
                      </div>
                    </div>
                  </div>
                </AnimatedCard>
              </motion.div>
              ))
            ) : (
              // Empty state
              <div className="col-span-full flex flex-col items-center justify-center py-16 text-center">
                <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full flex items-center justify-center mb-6">
                  <BookOpen className="h-12 w-12 text-blue-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  No courses found
                </h3>
                <p className="text-gray-600 dark:text-gray-400 max-w-md">
                  We're working on adding more courses. Check back soon!
                </p>
              </div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-12"
        >
          <AnimatedButton
            variant="outline"
            size="lg"
            className="group"
            onClick={() => {
              if (session) {
                window.location.href = '/student/courses';
              } else {
                window.location.href = '/auth/signin';
              }
            }}
          >
            {session ? 'View All Courses' : 'Sign In to View Courses'}
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </AnimatedButton>
        </motion.div>
      </div>
    </section>
  );
}
