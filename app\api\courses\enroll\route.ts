import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { createGraphyLearner, enrollLearnerInCourse, checkGraphyLearnerExists, GraphyApiError } from '@/lib/graphy-api'
import { getGraphyCourseUrl } from '@/lib/graphy-config'
import { z } from 'zod'

const enrollmentSchema = z.object({
  courseId: z.string().min(1, 'Course ID is required'),
  redirectUrl: z.string().url().optional()
})

// POST /api/courses/enroll - Enroll user in a course
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: enrollmentSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { courseId, redirectUrl } = validatedBody

      // Check if course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      if (!course.isActive) {
        return APIResponse.error('Course is not available', 400)
      }

      // Check if user is already enrolled
      const existingEnrollment = await prisma.enrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: courseId
          }
        }
      })

      if (existingEnrollment && existingEnrollment.status === 'ACTIVE') {
        return APIResponse.error('Already enrolled in this course', 400)
      }

      // Get user details
      const userDetails = await prisma.user.findUnique({
        where: { id: user.id },
        select: { email: true, name: true }
      })

      if (!userDetails?.email || !userDetails?.name) {
        return APIResponse.error('User email and name are required', 400)
      }

      try {
        // Step 1: Create learner on Graphy (handles existing learners gracefully)
        console.log('Creating/checking learner on Graphy:', userDetails.email)
        const learnerResult = await createGraphyLearner({
          email: userDetails.email,
          name: userDetails.name,
          sendEmail: true
        })

        console.log('Learner creation result:', learnerResult)

        // Step 2: Enroll learner in the course on Graphy
        console.log('Enrolling learner in course:', course.productId)
        const enrollmentResult = await enrollLearnerInCourse(
          userDetails.email,
          course.productId
        )

        console.log('Enrollment result:', enrollmentResult)

        // Step 3: Create or update enrollment record in our database
        const enrollmentData = {
          userId: user.id,
          courseId: courseId,
          status: 'ACTIVE' as const,
          enrolledAt: new Date(),
          progress: 0
        }

        if (existingEnrollment) {
          // Update existing enrollment
          await prisma.enrollment.update({
            where: { id: existingEnrollment.id },
            data: enrollmentData
          })
        } else {
          // Create new enrollment
          await prisma.enrollment.create({
            data: enrollmentData
          })
        }

        return APIResponse.success({
          message: enrollmentResult.message,
          redirectUrl: redirectUrl || enrollmentResult.redirectUrl,
          course: {
            id: course.id,
            title: course.title,
            price: course.price,
            slug: course.slug,
            productId: course.productId
          },
          enrollment: {
            status: 'ACTIVE',
            enrolledAt: new Date().toISOString(),
            progress: 0
          }
        })

      } catch (graphyError: any) {
        console.error('Graphy API error during enrollment:', graphyError)

        if (graphyError instanceof GraphyApiError) {
          // Create a pending enrollment for manual completion
          const enrollmentData = {
            userId: user.id,
            courseId: courseId,
            status: 'PENDING' as const,
            enrolledAt: new Date(),
            progress: 0
          }

          if (existingEnrollment) {
            await prisma.enrollment.update({
              where: { id: existingEnrollment.id },
              data: enrollmentData
            })
          } else {
            await prisma.enrollment.create({
              data: enrollmentData
            })
          }

          // Provide manual enrollment URL
          const manualEnrollUrl = getGraphyCourseUrl(course.slug)

          return APIResponse.success({
            message: 'Please complete enrollment manually on the course page',
            redirectUrl: redirectUrl || manualEnrollUrl,
            course: {
              id: course.id,
              title: course.title,
              price: course.price,
              slug: course.slug,
              productId: course.productId
            },
            enrollment: {
              status: 'PENDING',
              enrolledAt: new Date().toISOString(),
              progress: 0
            },
            requiresManualEnrollment: true,
            error: graphyError.message
          })
        }

        return APIResponse.error(
          'Failed to initiate enrollment. Please try again.',
          500
        )
      }

    } catch (error) {
      console.error('Error during course enrollment:', error)
      return APIResponse.error('Failed to enroll in course', 500)
    }
  }
)
