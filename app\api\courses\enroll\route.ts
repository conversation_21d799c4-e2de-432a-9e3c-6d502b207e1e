import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { createGraphyLearner, checkGraphyLearnerExists, GraphyApiError } from '@/lib/graphy-api'
import { getGraphyCourseUrl } from '@/lib/graphy-config'
import { z } from 'zod'

const enrollmentSchema = z.object({
  courseId: z.string().min(1, 'Course ID is required'),
  redirectUrl: z.string().url().optional()
})

// POST /api/courses/enroll - Enroll user in a course
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: enrollmentSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { courseId, redirectUrl } = validatedBody

      // Check if course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      if (!course.isActive) {
        return APIResponse.error('Course is not available', 400)
      }

      // Check if user is already enrolled
      const existingEnrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: courseId
          }
        }
      })

      if (existingEnrollment) {
        return APIResponse.error('Already enrolled in this course', 400)
      }

      // Get user details
      const userDetails = await prisma.user.findUnique({
        where: { id: user.id },
        select: { email: true, name: true }
      })

      if (!userDetails?.email || !userDetails?.name) {
        return APIResponse.error('User email and name are required', 400)
      }

      try {
        // Check if learner exists on Graphy
        const learnerExists = await checkGraphyLearnerExists(userDetails.email)

        // Create learner on Graphy if they don't exist
        if (!learnerExists) {
          await createGraphyLearner({
            email: userDetails.email,
            name: userDetails.name,
            sendEmail: true
          })
        }

        // Create enrollment record in our database
        await prisma.courseEnrollment.create({
          data: {
            userId: user.id,
            courseId: courseId,
            productId: course.productId,
            enrolledAt: new Date()
          }
        })

        // Generate Graphy course URL for payment/enrollment
        const graphyCourseUrl = getGraphyCourseUrl(course.slug)

        return APIResponse.success({
          message: 'Enrollment initiated successfully',
          redirectUrl: redirectUrl || graphyCourseUrl,
          course: {
            id: course.id,
            title: course.title,
            price: course.price,
            slug: course.slug
          }
        })

      } catch (graphyError) {
        console.error('Graphy API error during enrollment:', graphyError)
        
        if (graphyError instanceof GraphyApiError) {
          return APIResponse.error(
            'Failed to create account on learning platform: ' + graphyError.message,
            500
          )
        }
        
        return APIResponse.error(
          'Failed to initiate enrollment. Please try again.',
          500
        )
      }

    } catch (error) {
      console.error('Error during course enrollment:', error)
      return APIResponse.error('Failed to enroll in course', 500)
    }
  }
)
