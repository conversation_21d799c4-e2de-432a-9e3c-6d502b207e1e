import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { createGraphyLearner, checkGraphyLearnerExists, getGraphyCourseUrl, GraphyApiError } from '@/lib/graphy-api'
import { z } from 'zod'

const enrollmentSchema = z.object({
  courseId: z.string().min(1, 'Course ID is required'),
  redirectUrl: z.string().url().optional()
})

// POST /api/courses/enroll - Enroll user in a course
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: enrollmentSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { courseId, redirectUrl } = validatedBody

      // Check if course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      if (!course.isActive) {
        return APIResponse.error('Course is not available', 400)
      }

      // Check if user is already enrolled
      const existingEnrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: courseId
          }
        }
      })

      if (existingEnrollment && existingEnrollment.status === 'ACTIVE') {
        return APIResponse.error('Already enrolled in this course', 400)
      }

      // Get user details
      const userDetails = await prisma.user.findUnique({
        where: { id: user.id },
        select: { email: true, name: true }
      })

      if (!userDetails?.email || !userDetails?.name) {
        return APIResponse.error('User email and name are required', 400)
      }

      try {
        // Step 1: Check if learner exists on Graphy
        console.log('Checking if learner exists on Graphy:', userDetails.email)
        const learnerExists = await checkGraphyLearnerExists(userDetails.email)

        // Step 2: Create learner on Graphy if they don't exist
        if (!learnerExists) {
          console.log('Creating learner on Graphy:', userDetails.email)
          const learnerResult = await createGraphyLearner({
            email: userDetails.email,
            name: userDetails.name,
            sendEmail: true
          })
          console.log('Learner creation result:', learnerResult)

          // Mark user as created on Graphy
          await prisma.user.update({
            where: { id: user.id },
            data: { graphyCreated: true }
          })
        }

        // Step 3: Don't create enrollment record yet - wait for actual payment
        // We'll only track this in the response, not in the database
        // The enrollment will be created when we sync from Graphy after payment

        // Step 4: Generate Graphy course URL for payment/enrollment
        const graphyCourseUrl = getGraphyCourseUrl(course.slug)

        return APIResponse.success({
          message: 'Redirecting to course page for payment and enrollment',
          redirectUrl: redirectUrl || graphyCourseUrl,
          course: {
            id: course.id,
            title: course.title,
            price: course.price,
            slug: course.slug,
            productId: course.productId
          },
          requiresPayment: true,
          // Don't include enrollment data since user hasn't paid yet
          note: 'Enrollment will be created after successful payment on Graphy'
        })

      } catch (graphyError: any) {
        console.error('Graphy API error during enrollment:', graphyError)

        if (graphyError instanceof GraphyApiError) {
          // Create a pending enrollment for manual completion
          const enrollmentData = {
            userId: user.id,
            courseId: courseId,
            status: 'PENDING' as const,
            enrolledAt: new Date(),
            progress: 0
          }

          if (existingEnrollment) {
            await prisma.courseEnrollment.update({
              where: { id: existingEnrollment.id },
              data: {
                status: 'PENDING' as const,
                progress: 0
              }
            })
          } else {
            await prisma.courseEnrollment.create({
              data: {
                userId: user.id,
                courseId: courseId,
                productId: course.productId,
                status: 'PENDING' as const,
                enrolledAt: new Date(),
                progress: 0
              }
            })
          }

          // Provide manual enrollment URL
          const manualEnrollUrl = getGraphyCourseUrl(course.slug)

          return APIResponse.success({
            message: 'Please complete enrollment manually on the course page',
            redirectUrl: redirectUrl || manualEnrollUrl,
            course: {
              id: course.id,
              title: course.title,
              price: course.price,
              slug: course.slug,
              productId: course.productId
            },
            enrollment: {
              status: 'PENDING',
              enrolledAt: new Date().toISOString(),
              progress: 0
            },
            requiresManualEnrollment: true,
            error: graphyError.message
          })
        }

        return APIResponse.error(
          'Failed to initiate enrollment. Please try again.',
          500
        )
      }

    } catch (error) {
      console.error('Error during course enrollment:', error)
      return APIResponse.error('Failed to enroll in course', 500)
    }
  }
)
