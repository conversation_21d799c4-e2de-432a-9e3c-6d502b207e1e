import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { fetchGraphyCourses, GraphyApiError } from '@/lib/graphy-api'

// GET /api/public/courses - Get courses for public display (no auth required)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '6')
    const sync = searchParams.get('sync') === 'true'
    const category = searchParams.get('category')

    // If sync is requested, fetch fresh data from Graphy
    if (sync) {
      try {
        const graphyCourses = await fetchGraphyCourses({ limit: 50 })
        
        // Upsert courses in database
        for (const course of graphyCourses) {
          await prisma.course.upsert({
            where: { productId: course.productId },
            update: {
              title: course.title,
              description: course.description || null,
              price: course.price,
              slug: course.slug,
              thumbnailImage: course.thumbnailImage || null,
              category: course.category || null,
              duration: course.duration || null,
              instructor: course.instructor || null,
              rating: course.rating || null,
              studentsCount: course.studentsCount || null,
              features: course.features || [],
              updatedAt: new Date()
            },
            create: {
              productId: course.productId,
              title: course.title,
              description: course.description || null,
              price: course.price,
              slug: course.slug,
              thumbnailImage: course.thumbnailImage || null,
              category: course.category || null,
              duration: course.duration || null,
              instructor: course.instructor || null,
              rating: course.rating || null,
              studentsCount: course.studentsCount || null,
              features: course.features || []
            }
          })
        }
      } catch (error) {
        console.error('Failed to sync courses from Graphy:', error)
        // Continue with database query even if sync fails
      }
    }

    // Build where clause for filtering
    const where: any = {
      isActive: true
    }

    if (category && category !== 'all') {
      where.category = { equals: category, mode: 'insensitive' }
    }

    // Get courses from database
    const courses = await prisma.course.findMany({
      where,
      orderBy: [
        { rating: 'desc' },
        { studentsCount: 'desc' },
        { createdAt: 'desc' }
      ],
      take: limit,
      select: {
        id: true,
        productId: true,
        title: true,
        description: true,
        price: true,
        originalPrice: true,
        slug: true,
        thumbnailImage: true,
        category: true,
        duration: true,
        instructor: true,
        rating: true,
        studentsCount: true,
        features: true
      }
    })

    return APIResponse.success({
      courses: courses,
      total: courses.length
    })

  } catch (error) {
    console.error('Error fetching public courses:', error)
    
    if (error instanceof GraphyApiError) {
      return APIResponse.error(
        'Failed to fetch courses from Graphy: ' + error.message,
        500
      )
    }
    
    // Return empty result instead of error to prevent frontend crashes
    return APIResponse.success({
      courses: [],
      total: 0
    })
  }
}
