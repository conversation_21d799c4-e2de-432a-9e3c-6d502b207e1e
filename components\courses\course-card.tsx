'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  Star, 
  Users, 
  Clock, 
  BookOpen, 
  ArrowRight,
  CheckCircle,
  Play,
  Heart,
  Share2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { cn } from '@/lib/utils'

export interface Course {
  id: string
  productId: string
  title: string
  description?: string
  price: number
  originalPrice?: number
  slug: string
  thumbnailImage?: string
  category?: string
  duration?: string
  instructor?: string
  rating?: number
  studentsCount?: number
  features: string[]
  tags: string[]
  isEnrolled?: boolean
  enrollmentCount?: number
}

interface CourseCardProps {
  course: Course
  onEnroll?: (courseId: string) => void
  onViewDetails?: (courseId: string) => void
  onToggleFavorite?: (courseId: string) => void
  onShare?: (course: Course) => void
  isFavorite?: boolean
  isLoading?: boolean
  className?: string
}

export function CourseCard({
  course,
  onEnroll,
  onViewDetails,
  onToggleFavorite,
  onShare,
  isFavorite = false,
  isLoading = false,
  className
}: CourseCardProps) {
  const discountPercentage = course.originalPrice 
    ? Math.round(((course.originalPrice - course.price) / course.originalPrice) * 100)
    : 0

  const handleEnroll = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEnroll?.(course.id)
  }

  const handleViewDetails = () => {
    onViewDetails?.(course.id)
  }

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation()
    onToggleFavorite?.(course.id)
  }

  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation()
    onShare?.(course)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn("group cursor-pointer", className)}
      onClick={handleViewDetails}
    >
      <Card className="h-full overflow-hidden bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-[1.02]">
        {/* Course Image */}
        <div className="relative h-48 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 overflow-hidden">
          {course.thumbnailImage ? (
            <img
              src={course.thumbnailImage}
              alt={course.title}
              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <BookOpen className="h-16 w-16 text-white/80" />
            </div>
          )}
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-300" />
          
          {/* Top badges */}
          <div className="absolute top-4 left-4 flex gap-2">
            {course.category && (
              <Badge variant="secondary" className="bg-white/90 text-gray-900 font-medium">
                {course.category}
              </Badge>
            )}
            {discountPercentage > 0 && (
              <Badge className="bg-red-500 text-white font-bold">
                {discountPercentage}% OFF
              </Badge>
            )}
          </div>

          {/* Action buttons */}
          <div className="absolute top-4 right-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Button
              size="icon"
              variant="secondary"
              className="h-8 w-8 bg-white/90 hover:bg-white"
              onClick={handleToggleFavorite}
            >
              <Heart className={cn("h-4 w-4", isFavorite && "fill-red-500 text-red-500")} />
            </Button>
            <Button
              size="icon"
              variant="secondary"
              className="h-8 w-8 bg-white/90 hover:bg-white"
              onClick={handleShare}
            >
              <Share2 className="h-4 w-4" />
            </Button>
          </div>

          {/* Enrollment status */}
          {course.isEnrolled && (
            <div className="absolute bottom-4 left-4">
              <Badge className="bg-green-500 text-white">
                <CheckCircle className="h-3 w-3 mr-1" />
                Enrolled
              </Badge>
            </div>
          )}
        </div>

        <CardHeader className="pb-3">
          <div className="space-y-2">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
              {course.title}
            </h3>
            
            {course.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                {course.description}
              </p>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Instructor */}
          {course.instructor && (
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs">
                  {course.instructor.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {course.instructor}
              </span>
            </div>
          )}

          {/* Course stats */}
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center gap-4">
              {course.rating && (
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">{course.rating}</span>
                </div>
              )}
              
              {course.studentsCount && (
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>{course.studentsCount.toLocaleString()}</span>
                </div>
              )}
              
              {course.duration && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>{course.duration}</span>
                </div>
              )}
            </div>
          </div>

          {/* Features */}
          {course.features.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {course.features.slice(0, 3).map((feature, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800"
                >
                  {feature}
                </Badge>
              ))}
              {course.features.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{course.features.length - 3} more
                </Badge>
              )}
            </div>
          )}
        </CardContent>

        <CardFooter className="pt-0">
          <div className="w-full space-y-3">
            {/* Price */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-gray-900 dark:text-white">
                  ₹{course.price}
                </span>
                {course.originalPrice && course.originalPrice > course.price && (
                  <span className="text-sm text-gray-500 line-through">
                    ₹{course.originalPrice}
                  </span>
                )}
              </div>
            </div>

            {/* Action button */}
            <Button
              className={cn(
                "w-full transition-all duration-300",
                course.isEnrolled
                  ? "bg-green-500 hover:bg-green-600 text-white"
                  : "bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white"
              )}
              onClick={handleEnroll}
              disabled={isLoading}
            >
              {isLoading ? (
                "Loading..."
              ) : course.isEnrolled ? (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Continue Learning
                </>
              ) : (
                <>
                  Enroll Now
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  )
}
