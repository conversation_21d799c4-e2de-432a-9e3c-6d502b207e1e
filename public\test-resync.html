<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Course Resync</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #721c24;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .loading {
            color: #856404;
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .slug-change {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .slug-old {
            color: #dc3545;
            font-family: monospace;
        }
        .slug-new {
            color: #28a745;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Course Resync</h1>
        <p>This tool tests the course resync functionality with the new slug format:</p>
        <p><strong>Format:</strong> <code>course-title-in-lowercase-with-hyphens-productid</code></p>
        <p><strong>Example:</strong> <code>anm-nursing-batch-2025-for-all-nursing-exam-558a70b9</code></p>
        
        <div>
            <button class="button" onclick="checkStatus()">Check Status</button>
            <button class="button" onclick="debugSlugs()">Debug Slugs</button>
            <button class="button" onclick="testResync()">🚀 Test Resync (3 courses)</button>
        </div>
    </div>

    <div class="container">
        <h2>📊 Results</h2>
        <div id="results"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('results');

        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function checkStatus() {
            clearResults();
            log('🔍 Checking current status...', 'loading');
            
            try {
                const response = await fetch('/api/debug/resync-test');
                const result = await response.json();
                
                if (response.ok) {
                    const data = result.data || result;
                    log('✅ Status check completed', 'success');
                    log(`📚 Total courses: ${data.summary.totalCourses}`);
                    
                    if (data.sampleCourses && data.sampleCourses.length > 0) {
                        log('<h3>📋 Recent Courses:</h3>');
                        data.sampleCourses.forEach(course => {
                            log(`<div class="slug-change">
                                <strong>${course.title}</strong><br>
                                Product ID: ${course.productId}<br>
                                Slug: <span class="slug-new">${course.slug}</span> (${course.slugLength} chars)<br>
                                Updated: ${new Date(course.lastUpdated).toLocaleString()}
                            </div>`);
                        });
                    }
                } else {
                    log(`❌ Status check failed: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function debugSlugs() {
            clearResults();
            log('🔍 Debugging slugs...', 'loading');
            
            try {
                const response = await fetch('/api/debug/course-slugs');
                const result = await response.json();
                
                if (response.ok) {
                    const data = result.data || result;
                    log('✅ Debug completed', 'success');
                    log(`📊 Issues found: ${data.summary.issuesFound}`);
                    
                    if (data.issues && data.issues.length > 0) {
                        log('<h3>🚨 Issues:</h3>');
                        data.issues.forEach(issue => {
                            log(`<div class="slug-change">
                                <strong>${issue.title}</strong><br>
                                Raw: <span class="slug-old">${issue.raw.originalSlug || 'MISSING'}</span><br>
                                DB: <span class="slug-new">${issue.database?.storedSlug || 'NOT IN DB'}</span><br>
                                Match: ${issue.database?.matchesRaw ? '✅' : '❌'}
                            </div>`);
                        });
                    } else {
                        log('✅ No issues found - all slugs are correct!', 'success');
                    }
                } else {
                    log(`❌ Debug failed: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function testResync() {
            clearResults();
            log('🚀 Starting test resync...', 'loading');
            
            try {
                const response = await fetch('/api/debug/resync-test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    const data = result.data || result;
                    log('🎉 Test resync completed!', 'success');
                    log(`📊 Processed: ${data.summary.totalProcessed}, Updated: ${data.summary.updated}, Created: ${data.summary.created}`);
                    
                    if (data.slugChanges && data.slugChanges.length > 0) {
                        log('<h3>🔄 Slug Changes:</h3>');
                        data.slugChanges.forEach(change => {
                            log(`<div class="slug-change">
                                <strong>${change.title}</strong><br>
                                Old: <span class="slug-old">${change.oldSlug}</span> (${change.oldLength} chars)<br>
                                New: <span class="slug-new">${change.newSlug}</span> (${change.newLength} chars)
                            </div>`);
                        });
                    } else {
                        log('✅ No slug changes needed!', 'success');
                    }
                } else {
                    log(`❌ Resync failed: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Auto-run status check
        window.addEventListener('load', () => {
            setTimeout(checkStatus, 1000);
        });
    </script>
</body>
</html>
