import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { fetchGraphyLearnerEnrollments, GraphyApiError } from '@/lib/graphy-api'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  status: z.enum(['active', 'completed', 'paused', 'all']).optional().default('all'),
  sync: z.enum(['true', 'false']).optional().default('false')
})

// GET /api/courses/my-courses - Get user's enrolled courses
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const { page = 1, limit = 20, status, sync } = validatedQuery

      // Get user details
      const userDetails = await prisma.user.findUnique({
        where: { id: user.id },
        select: { email: true }
      })

      if (!userDetails?.email) {
        return APIResponse.error('User email not found', 400)
      }

      // If sync is requested, fetch fresh enrollment data from Graphy
      if (sync === 'true') {
        try {
          const graphyEnrollments = await fetchGraphyLearnerEnrollments(userDetails.email)
          
          // Update enrollment records with Graphy data
          for (const enrollment of graphyEnrollments) {
            // Find the course in our database
            const course = await prisma.course.findUnique({
              where: { productId: enrollment.productId }
            })

            if (course) {
              // Update or create enrollment record
              await prisma.courseEnrollment.upsert({
                where: {
                  userId_courseId: {
                    userId: user.id,
                    courseId: course.id
                  }
                },
                update: {
                  progress: enrollment.progress || 0,
                  status: enrollment.status || 'active',
                  lastAccessedAt: new Date()
                },
                create: {
                  userId: user.id,
                  courseId: course.id,
                  productId: enrollment.productId,
                  enrolledAt: new Date(enrollment.enrolledAt),
                  progress: enrollment.progress || 0,
                  status: enrollment.status || 'active'
                }
              })
            }
          }
        } catch (error) {
          console.error('Failed to sync enrollments from Graphy:', error)
          // Continue with database query even if sync fails
        }
      }

      // Build where clause for filtering
      const where: any = {
        userId: user.id
      }

      if (status !== 'all') {
        where.status = status
      }

      // Get total count for pagination
      const total = await prisma.courseEnrollment.count({ where })

      // Get enrollments with course details
      const enrollments = await prisma.courseEnrollment.findMany({
        where,
        include: {
          course: {
            select: {
              id: true,
              productId: true,
              title: true,
              description: true,
              price: true,
              originalPrice: true,
              slug: true,
              thumbnailImage: true,
              category: true,
              duration: true,
              instructor: true,
              rating: true,
              studentsCount: true,
              features: true,
              tags: true
            }
          }
        },
        orderBy: [
          { lastAccessedAt: 'desc' },
          { enrolledAt: 'desc' }
        ],
        skip: (page - 1) * limit,
        take: limit
      })

      // Transform data for response
      const coursesWithProgress = enrollments.map(enrollment => ({
        enrollmentId: enrollment.id,
        enrolledAt: enrollment.enrolledAt,
        progress: enrollment.progress,
        status: enrollment.status,
        lastAccessedAt: enrollment.lastAccessedAt,
        completedAt: enrollment.completedAt,
        course: enrollment.course
      }))

      // Calculate summary statistics
      const totalEnrollments = await prisma.courseEnrollment.count({
        where: { userId: user.id }
      })

      const completedCourses = await prisma.courseEnrollment.count({
        where: { 
          userId: user.id,
          status: 'completed'
        }
      })

      const activeCourses = await prisma.courseEnrollment.count({
        where: { 
          userId: user.id,
          status: 'active'
        }
      })

      const averageProgress = await prisma.courseEnrollment.aggregate({
        where: { 
          userId: user.id,
          status: { not: 'completed' }
        },
        _avg: {
          progress: true
        }
      })

      return APIResponse.success({
        enrollments: coursesWithProgress,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        },
        summary: {
          totalEnrollments,
          completedCourses,
          activeCourses,
          averageProgress: Math.round(averageProgress._avg.progress || 0)
        }
      })

    } catch (error) {
      console.error('Error fetching user courses:', error)

      if (error instanceof GraphyApiError) {
        return APIResponse.error(
          'Failed to fetch courses from learning platform: ' + error.message,
          500
        )
      }

      // Return empty result instead of error to prevent frontend crashes
      return APIResponse.success({
        enrollments: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        },
        summary: {
          totalEnrollments: 0,
          completedCourses: 0,
          activeCourses: 0,
          averageProgress: 0
        }
      })
    }
  }
)
