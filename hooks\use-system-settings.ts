'use client';

import { useState, useEffect, useCallback } from 'react';

export interface TeamMember {
  id: string;
  name: string;
  title: string;
  bio: string;
  image: string;
  expertise: string[];
}

export interface SystemSettings {
  companyName: string;
  companyDescription: string;
  companyMission: string;
  companyVision: string;
  contactEmail: string;
  contactPhone: string;
  contactAddress: string;
  supportEmail: string;
  youtubeChannel?: string;
  facebookPage?: string;
  twitterHandle?: string;
  instagramHandle?: string;
  linkedinPage?: string;
  logoUrl?: string;
  faviconUrl?: string;
  brandColor: string;
  businessHours: string;
  teamMembers: TeamMember[];
  totalStudents: number;
  successRate: number;
  coursesOffered: number;
}

const defaultSettings: SystemSettings = {
  companyName: 'ExamAce',
  companyDescription: 'India\'s leading online coaching platform',
  companyMission: 'Empowering students to achieve their dreams through quality education',
  companyVision: 'To be the most trusted educational platform in India',
  contactEmail: '<EMAIL>',
  contactPhone: '+91 98765 43210',
  contactAddress: 'New Delhi, India',
  supportEmail: '<EMAIL>',
  youtubeChannel: 'https://www.youtube.com/@YourChannelName',
  facebookPage: '',
  twitterHandle: '',
  instagramHandle: '',
  linkedinPage: '',
  logoUrl: '',
  faviconUrl: '',
  brandColor: '#8B5CF6',
  businessHours: 'Mon-Fri, 9 AM - 6 PM IST',
  teamMembers: [
    {
      id: '1',
      name: 'Rajesh Kumar',
      title: 'Founder & CEO',
      bio: 'Former IIT graduate with 15+ years in education technology. Passionate about making quality education accessible to every student in India.',
      image: '/images/team/founder.jpg',
      expertise: ['Education Technology', 'Strategic Planning', 'Team Leadership']
    },
    {
      id: '2',
      name: 'Priya Sharma',
      title: 'Co-Founder & CTO',
      bio: 'Ex-Google engineer with expertise in AI and machine learning. Leading the technical innovation at ExamAce to create personalized learning experiences.',
      image: '/images/team/cofounder.jpg',
      expertise: ['Artificial Intelligence', 'Software Architecture', 'Product Development']
    }
  ],
  totalStudents: 500000,
  successRate: 98,
  coursesOffered: 50
};

// Global cache for settings
let globalSettingsCache: SystemSettings | null = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export function useSystemSettings() {
  const [settings, setSettings] = useState<SystemSettings>(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      // Use cached data if available and not expired
      const now = Date.now();
      if (!forceRefresh && globalSettingsCache && (now - cacheTimestamp) < CACHE_DURATION) {
        setSettings(globalSettingsCache);
        setLoading(false);
        return globalSettingsCache;
      }

      const response = await fetch('/api/system-settings');
      
      if (!response.ok) {
        throw new Error('Failed to fetch system settings');
      }

      const data = await response.json();
      const fetchedSettings = data.data?.settings || data.settings || defaultSettings;

      // Update global cache
      globalSettingsCache = fetchedSettings;
      cacheTimestamp = now;

      setSettings(fetchedSettings);
      return fetchedSettings;
    } catch (err: any) {
      console.error('Error fetching system settings:', err);
      setError(err.message);
      // Use default settings on error
      setSettings(defaultSettings);
      return defaultSettings;
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshCache = useCallback(async () => {
    try {
      const response = await fetch('/api/system-settings/refresh-cache', {
        method: 'POST'
      });
      
      if (response.ok) {
        await fetchSettings(true);
      }
    } catch (err) {
      console.error('Error refreshing settings cache:', err);
    }
  }, [fetchSettings]);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  return {
    settings,
    loading,
    error,
    refetch: fetchSettings,
    refreshCache
  };
}

// Hook for admin settings management
export function useAdminSystemSettings() {
  const [settings, setSettings] = useState<SystemSettings>(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/system-settings');
      
      if (!response.ok) {
        throw new Error('Failed to fetch admin system settings');
      }

      const data = await response.json();
      const fetchedSettings = data.data?.settings || data.settings || defaultSettings;

      setSettings(fetchedSettings);
      return fetchedSettings;
    } catch (err: any) {
      console.error('Error fetching admin system settings:', err);
      setError(err.message);
      setSettings(defaultSettings);
      return defaultSettings;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateSettings = useCallback(async (newSettings: Partial<SystemSettings>) => {
    try {
      setSaving(true);
      setError(null);

      const response = await fetch('/api/admin/system-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ ...settings, ...newSettings })
      });

      if (!response.ok) {
        throw new Error('Failed to update system settings');
      }

      const data = await response.json();
      const updatedSettings = data.data?.settings || data.settings;

      setSettings(updatedSettings);

      // Clear global cache to force refresh on public pages
      globalSettingsCache = null;
      cacheTimestamp = 0;

      return updatedSettings;
    } catch (err: any) {
      console.error('Error updating system settings:', err);
      setError(err.message);
      throw err;
    } finally {
      setSaving(false);
    }
  }, [settings]);

  const resetSettings = useCallback(async () => {
    try {
      setSaving(true);
      setError(null);

      const response = await fetch('/api/admin/system-settings/reset', {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error('Failed to reset system settings');
      }

      const data = await response.json();
      const resetSettings = data.data?.settings || data.settings || defaultSettings;

      setSettings(resetSettings);

      // Clear global cache
      globalSettingsCache = null;
      cacheTimestamp = 0;

      return resetSettings;
    } catch (err: any) {
      console.error('Error resetting system settings:', err);
      setError(err.message);
      throw err;
    } finally {
      setSaving(false);
    }
  }, []);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  return {
    settings,
    loading,
    saving,
    error,
    updateSettings,
    resetSettings,
    refetch: fetchSettings
  };
}

// Utility function to get settings synchronously (for SSR or when settings are already loaded)
export function getSystemSettingsSync(): SystemSettings {
  return globalSettingsCache || defaultSettings;
}
