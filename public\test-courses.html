<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .course-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .course-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .course-price {
            color: #007bff;
            font-size: 18px;
            font-weight: bold;
        }
        .course-meta {
            color: #666;
            font-size: 14px;
            margin: 5px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Course API Test Page</h1>
        <p>This page tests the course API endpoints to help debug why courses aren't showing up.</p>
        
        <div>
            <button class="button" onclick="testPublicAPI()">Test Public API</button>
            <button class="button" onclick="testPrivateAPI()">Test Private API (requires auth)</button>
            <button class="button" onclick="testGraphyDirect()">Test Graphy Direct</button>
            <button class="button" onclick="clearResults()">Clear Results</button>
        </div>
    </div>

    <div class="container">
        <h2>📊 Test Results</h2>
        <div id="results"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('results');

        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function testPublicAPI() {
            log('🔍 Testing Public API: /api/public/courses', 'loading');
            
            try {
                const response = await fetch('/api/public/courses?limit=6&sync=true');
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ Public API Success! Status: ${response.status}`, 'success');
                    log(`📦 Found ${data.courses?.length || 0} courses`);
                    
                    if (data.courses && data.courses.length > 0) {
                        data.courses.forEach((course, index) => {
                            const courseHtml = `
                                <div class="course-card">
                                    <div class="course-title">${course.title}</div>
                                    <div class="course-price">₹${course.price}</div>
                                    <div class="course-meta">
                                        Category: ${course.category || 'N/A'} | 
                                        Slug: ${course.slug || 'N/A'} | 
                                        Product ID: ${course.productId}
                                    </div>
                                    <div class="course-meta">
                                        Instructor: ${course.instructor || 'N/A'} | 
                                        Students: ${course.studentsCount || 'N/A'}
                                    </div>
                                </div>
                            `;
                            log(courseHtml);
                        });
                    } else {
                        log('⚠️ No courses found in response', 'error');
                    }
                    
                    log('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                } else {
                    log(`❌ Public API Error! Status: ${response.status}`, 'error');
                    log('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                }
            } catch (error) {
                log(`❌ Public API Request Failed: ${error.message}`, 'error');
                console.error('Public API Error:', error);
            }
        }

        async function testPrivateAPI() {
            log('🔍 Testing Private API: /api/courses', 'loading');
            
            try {
                const response = await fetch('/api/courses?limit=6&sync=true');
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ Private API Success! Status: ${response.status}`, 'success');
                    log(`📦 Found ${data.courses?.length || 0} courses`);
                    log('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                } else {
                    log(`❌ Private API Error! Status: ${response.status}`, 'error');
                    log('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                }
            } catch (error) {
                log(`❌ Private API Request Failed: ${error.message}`, 'error');
                console.error('Private API Error:', error);
            }
        }

        async function testGraphyDirect() {
            log('🔍 Testing Graphy API Direct', 'loading');
            
            const graphyUrl = 'https://api.ongraphy.com/public/v1/products?mid=nextgenclasses&key=4936e29c-3b06-48bd-976b-c4a38bcdac09&limit=5';
            
            try {
                // Note: This might fail due to CORS, but we can try
                const response = await fetch(graphyUrl);
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ Graphy API Success! Status: ${response.status}`, 'success');
                    log(`📦 Found ${data.data?.length || 0} courses from Graphy`);
                    log('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                } else {
                    log(`❌ Graphy API Error! Status: ${response.status}`, 'error');
                    log('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                }
            } catch (error) {
                log(`❌ Graphy API Request Failed (likely CORS): ${error.message}`, 'error');
                log('💡 This is expected due to CORS policy. The server-side API should work fine.');
                console.error('Graphy API Error:', error);
            }
        }

        // Auto-run public API test on page load
        window.addEventListener('load', () => {
            log('🚀 Page loaded. Running initial test...', 'loading');
            setTimeout(testPublicAPI, 1000);
        });
    </script>
</body>
</html>
