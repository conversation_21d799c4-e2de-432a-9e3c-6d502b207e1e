'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Home, BookOpen, Users, HelpCircle, Shield, FileText, RefreshCw, Mail } from 'lucide-react';
import Link from 'next/link';

export default function SitemapPage() {
  const siteStructure = [
    {
      category: 'Main Pages',
      icon: Home,
      color: 'from-blue-500 to-cyan-500',
      pages: [
        { title: 'Home', href: '/', description: 'Main landing page with course overview' },
        { title: 'Contact', href: '/contact', description: 'Get in touch with our support team' },
        { title: 'Help Center', href: '/help', description: 'Find answers to common questions' },
        { title: 'Sitemap', href: '/sitemap', description: 'Complete site navigation guide' }
      ]
    },
    {
      category: 'Student Portal',
      icon: Users,
      color: 'from-green-500 to-emerald-500',
      pages: [
        { title: 'Student Dashboard', href: '/student', description: 'Main student dashboard and overview' },
        { title: 'My Courses', href: '/student/my-courses', description: 'View enrolled courses and progress' },
        { title: 'Browse Courses', href: '/student/courses', description: 'Explore and enroll in new courses' },
        { title: 'Quiz Schedule', href: '/student/schedule', description: 'View scheduled quizzes and tests' },
        { title: 'Student Chat', href: '/student/chat', description: 'Connect with other students' },
        { title: 'Profile Settings', href: '/student/profile', description: 'Manage your account settings' }
      ]
    },
    {
      category: 'Admin Portal',
      icon: Shield,
      color: 'from-purple-500 to-violet-500',
      pages: [
        { title: 'Admin Dashboard', href: '/admin', description: 'Administrative overview and controls' },
        { title: 'User Management', href: '/admin/users', description: 'Manage student and admin accounts' },
        { title: 'Quiz Management', href: '/admin/quizzes', description: 'Create and manage quizzes' },
        { title: 'Quiz Scheduling', href: '/admin/scheduling', description: 'Schedule quizzes and tests' },
        { title: 'Settings', href: '/admin/settings', description: 'Platform configuration and settings' }
      ]
    },
    {
      category: 'Authentication',
      icon: Users,
      color: 'from-orange-500 to-red-500',
      pages: [
        { title: 'Sign In', href: '/auth/signin', description: 'Login to your account' },
        { title: 'Sign Up', href: '/auth/signup', description: 'Create a new account' },
        { title: 'Forgot Password', href: '/auth/forgot-password', description: 'Reset your password' }
      ]
    },
    {
      category: 'Legal & Support',
      icon: FileText,
      color: 'from-gray-500 to-slate-500',
      pages: [
        { title: 'Privacy Policy', href: '/privacy', description: 'How we protect your personal information' },
        { title: 'Terms of Service', href: '/terms', description: 'Terms and conditions for using our platform' },
        { title: 'Refund Policy', href: '/refund', description: 'Our refund and cancellation policy' },
        { title: 'Help Center', href: '/help', description: 'Comprehensive help and FAQ section' }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Home
          </Link>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-violet-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6">
            Site Map
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Navigate through all pages and features of the ExamAce platform. Find exactly what you're looking for.
          </p>
        </motion.div>

        {/* Site Structure */}
        <div className="space-y-12">
          {siteStructure.map((section, sectionIndex) => (
            <motion.div
              key={section.category}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 + sectionIndex * 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center gap-4 mb-8">
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${section.color} flex items-center justify-center`}>
                  <section.icon className="w-6 h-6 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {section.category}
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {section.pages.map((page, pageIndex) => (
                  <motion.div
                    key={page.href}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 + sectionIndex * 0.1 + pageIndex * 0.05 }}
                  >
                    <Link
                      href={page.href}
                      className="block p-4 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-violet-300 dark:hover:border-violet-600 hover:bg-violet-50 dark:hover:bg-violet-900/20 transition-all duration-300 group"
                    >
                      <h3 className="font-semibold text-gray-900 dark:text-white group-hover:text-violet-600 dark:group-hover:text-violet-400 transition-colors mb-2">
                        {page.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                        {page.description}
                      </p>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quick Links */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="bg-gradient-to-r from-violet-500 to-purple-600 rounded-2xl p-8 text-white mt-12"
        >
          <h2 className="text-2xl font-bold mb-4 text-center">Quick Access</h2>
          <p className="text-violet-100 text-center mb-8">
            Jump to the most commonly used sections of our platform
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link
              href="/student"
              className="bg-white/20 hover:bg-white/30 rounded-lg p-4 text-center transition-colors group"
            >
              <Users className="w-8 h-8 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium">Student Portal</span>
            </Link>
            
            <Link
              href="/student/courses"
              className="bg-white/20 hover:bg-white/30 rounded-lg p-4 text-center transition-colors group"
            >
              <BookOpen className="w-8 h-8 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium">Browse Courses</span>
            </Link>
            
            <Link
              href="/help"
              className="bg-white/20 hover:bg-white/30 rounded-lg p-4 text-center transition-colors group"
            >
              <HelpCircle className="w-8 h-8 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium">Help Center</span>
            </Link>
            
            <Link
              href="/contact"
              className="bg-white/20 hover:bg-white/30 rounded-lg p-4 text-center transition-colors group"
            >
              <Mail className="w-8 h-8 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium">Contact Us</span>
            </Link>
          </div>
        </motion.div>

        {/* Search Suggestion */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
          className="text-center mt-12"
        >
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Can't find what you're looking for?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/help"
              className="inline-flex items-center gap-2 bg-violet-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-violet-600 transition-colors"
            >
              <HelpCircle className="w-5 h-5" />
              Search Help Center
            </Link>
            
            <Link
              href="/contact"
              className="inline-flex items-center gap-2 border-2 border-violet-500 text-violet-500 px-6 py-3 rounded-lg font-semibold hover:bg-violet-500 hover:text-white transition-colors"
            >
              <Mail className="w-5 h-5" />
              Contact Support
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
