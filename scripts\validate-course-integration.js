#!/usr/bin/env node

/**
 * Course Integration Validation Script
 * 
 * This script validates that all course integration components are properly set up.
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 Validating Course Integration...\n')

// Check if files exist
const requiredFiles = [
  'lib/graphy-config.ts',
  'lib/graphy-api.ts',
  'lib/course-service.ts',
  'components/courses/course-card.tsx',
  'components/courses/course-grid.tsx',
  'components/courses/course-filters.tsx',
  'components/courses/course-error-boundary.tsx',
  'components/dashboard/course-widgets.tsx',
  'app/api/courses/route.ts',
  'app/api/courses/enroll/route.ts',
  'app/api/courses/my-courses/route.ts',
  'app/api/courses/categories/route.ts',
  'app/student/courses/page.tsx',
  'app/student/my-courses/page.tsx'
]

let allFilesExist = true

console.log('📁 Checking required files...')
requiredFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file)
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file} - MISSING`)
    allFilesExist = false
  }
})

// Check environment variables
console.log('\n🔧 Checking environment configuration...')
const envPath = path.join(process.cwd(), '.env.example')
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8')
  const requiredEnvVars = [
    'GRAPHY_API_KEY',
    'GRAPHY_MID',
    'GRAPHY_SUBDOMAIN',
    'NEXT_PUBLIC_GRAPHY_SUBDOMAIN'
  ]
  
  requiredEnvVars.forEach(envVar => {
    if (envContent.includes(envVar)) {
      console.log(`✅ ${envVar} configured in .env.example`)
    } else {
      console.log(`❌ ${envVar} - MISSING from .env.example`)
    }
  })
} else {
  console.log('❌ .env.example file not found')
}

// Check Prisma schema
console.log('\n🗄️  Checking database schema...')
const schemaPath = path.join(process.cwd(), 'prisma/schema.prisma')
if (fs.existsSync(schemaPath)) {
  const schemaContent = fs.readFileSync(schemaPath, 'utf8')
  
  if (schemaContent.includes('model Course')) {
    console.log('✅ Course model found in schema')
  } else {
    console.log('❌ Course model - MISSING from schema')
  }
  
  if (schemaContent.includes('model CourseEnrollment')) {
    console.log('✅ CourseEnrollment model found in schema')
  } else {
    console.log('❌ CourseEnrollment model - MISSING from schema')
  }
  
  if (schemaContent.includes('courseEnrollments CourseEnrollment[]')) {
    console.log('✅ User-CourseEnrollment relationship found')
  } else {
    console.log('❌ User-CourseEnrollment relationship - MISSING')
  }
} else {
  console.log('❌ Prisma schema file not found')
}

// Check navigation updates
console.log('\n🧭 Checking navigation updates...')
const layoutPath = path.join(process.cwd(), 'components/student/student-layout.tsx')
if (fs.existsSync(layoutPath)) {
  const layoutContent = fs.readFileSync(layoutPath, 'utf8')
  
  if (layoutContent.includes('/student/courses')) {
    console.log('✅ Courses navigation link found')
  } else {
    console.log('❌ Courses navigation link - MISSING')
  }
  
  if (layoutContent.includes('/student/my-courses')) {
    console.log('✅ My Courses navigation link found')
  } else {
    console.log('❌ My Courses navigation link - MISSING')
  }
  
  if (layoutContent.includes('GraduationCap') && layoutContent.includes('Library')) {
    console.log('✅ Course icons imported')
  } else {
    console.log('❌ Course icons - MISSING')
  }
} else {
  console.log('❌ Student layout file not found')
}

// Check dashboard updates
console.log('\n📊 Checking dashboard integration...')
const dashboardPath = path.join(process.cwd(), 'app/student/page.tsx')
if (fs.existsSync(dashboardPath)) {
  const dashboardContent = fs.readFileSync(dashboardPath, 'utf8')
  
  if (dashboardContent.includes('EnrolledCoursesWidget')) {
    console.log('✅ EnrolledCoursesWidget integrated')
  } else {
    console.log('❌ EnrolledCoursesWidget - MISSING from dashboard')
  }
  
  if (dashboardContent.includes('CourseStatsWidget')) {
    console.log('✅ CourseStatsWidget integrated')
  } else {
    console.log('❌ CourseStatsWidget - MISSING from dashboard')
  }
  
  if (dashboardContent.includes('RecommendedCoursesWidget')) {
    console.log('✅ RecommendedCoursesWidget integrated')
  } else {
    console.log('❌ RecommendedCoursesWidget - MISSING from dashboard')
  }
} else {
  console.log('❌ Student dashboard file not found')
}

// Summary
console.log('\n📋 Validation Summary:')
if (allFilesExist) {
  console.log('✅ All required files are present')
} else {
  console.log('❌ Some required files are missing')
}

console.log('\n🚀 Next Steps:')
console.log('1. Set up your Graphy API credentials in .env.local')
console.log('2. Run: npx prisma db push')
console.log('3. Run: npx prisma generate')
console.log('4. Start the development server: npm run dev')
console.log('5. Test the course integration manually')

console.log('\n📖 For detailed testing instructions, see: test-course-integration.md')

// Check package.json for required dependencies
console.log('\n📦 Checking dependencies...')
const packagePath = path.join(process.cwd(), 'package.json')
if (fs.existsSync(packagePath)) {
  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
  const dependencies = { ...packageContent.dependencies, ...packageContent.devDependencies }
  
  const requiredDeps = [
    'framer-motion',
    'lucide-react',
    'sonner',
    '@prisma/client',
    'prisma'
  ]
  
  requiredDeps.forEach(dep => {
    if (dependencies[dep]) {
      console.log(`✅ ${dep} - ${dependencies[dep]}`)
    } else {
      console.log(`❌ ${dep} - MISSING`)
    }
  })
} else {
  console.log('❌ package.json not found')
}

console.log('\n✨ Course integration validation complete!')
