<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Course Resync</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #721c24;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .loading {
            color: #856404;
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .slug-change {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .slug-old {
            color: #dc3545;
            font-family: monospace;
        }
        .slug-new {
            color: #28a745;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Admin - Course Resync Tool</h1>
        <p>This tool will resync all courses from Graphy and ensure original slugs are preserved.</p>
        
        <div>
            <button class="button" onclick="checkStatus()" id="statusBtn">Check Current Status</button>
            <button class="button" onclick="debugSlugs()" id="debugBtn">Debug Slugs</button>
            <button class="button" onclick="resyncCourses()" id="resyncBtn">🚀 Resync All Courses</button>
        </div>
    </div>

    <div class="container">
        <h2>📊 Results</h2>
        <div id="results"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('results');

        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function checkStatus() {
            clearResults();
            log('🔍 Checking current course status...', 'loading');
            
            try {
                const response = await fetch('/api/admin/resync-courses');
                const result = await response.json();
                
                if (response.ok) {
                    const data = result.data || result;
                    log('✅ Status check completed', 'success');
                    log(`📚 Total courses in database: ${data.summary.totalCourses}`);
                    log(`🕒 Last updated: ${data.summary.lastUpdated || 'Never'}`);
                    
                    if (data.sampleCourses && data.sampleCourses.length > 0) {
                        log('<h3>📋 Sample Courses:</h3>');
                        data.sampleCourses.forEach(course => {
                            log(`<div class="slug-change">
                                <strong>${course.title}</strong><br>
                                Product ID: ${course.productId}<br>
                                Slug: <span class="slug-new">${course.slug}</span> (${course.slugLength} chars)<br>
                                Last Updated: ${new Date(course.lastUpdated).toLocaleString()}
                            </div>`);
                        });
                    }
                } else {
                    log(`❌ Status check failed: ${result.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function debugSlugs() {
            clearResults();
            log('🔍 Debugging slug preservation...', 'loading');
            
            try {
                const response = await fetch('/api/debug/course-slugs');
                const result = await response.json();
                
                if (response.ok) {
                    const data = result.data || result;
                    log('✅ Slug debug completed', 'success');
                    log(`📊 Summary: ${data.summary.rawCoursesCount} raw, ${data.summary.transformedCoursesCount} transformed, ${data.summary.dbCoursesCount} in DB`);
                    log(`⚠️ Issues found: ${data.summary.issuesFound}`);
                    
                    if (data.issues && data.issues.length > 0) {
                        log('<h3>🚨 Slug Issues Found:</h3>');
                        data.issues.forEach(issue => {
                            log(`<div class="slug-change">
                                <strong>${issue.title}</strong><br>
                                Product ID: ${issue.productId}<br>
                                Raw Slug: <span class="slug-old">${issue.raw.originalSlug || 'MISSING'}</span> (${issue.raw.slugLength} chars)<br>
                                DB Slug: <span class="slug-new">${issue.database?.storedSlug || 'NOT IN DB'}</span> (${issue.database?.slugLength || 0} chars)<br>
                                Matches: ${issue.database?.matchesRaw ? '✅' : '❌'}
                            </div>`);
                        });
                    }
                    
                    log('<h3>📋 Detailed Comparison:</h3>');
                    log('<pre>' + JSON.stringify(data.comparison, null, 2) + '</pre>');
                } else {
                    log(`❌ Debug failed: ${result.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function resyncCourses() {
            clearResults();
            log('🚀 Starting course resync...', 'loading');
            
            const resyncBtn = document.getElementById('resyncBtn');
            resyncBtn.disabled = true;
            resyncBtn.textContent = '⏳ Resyncing...';
            
            try {
                const response = await fetch('/api/admin/resync-courses', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    const data = result.data || result;
                    log('🎉 Course resync completed successfully!', 'success');
                    log(`📊 Summary: ${data.summary.totalProcessed} processed, ${data.summary.created} created, ${data.summary.updated} updated`);
                    
                    if (data.summary.errors > 0) {
                        log(`⚠️ Errors: ${data.summary.errors}`, 'error');
                    }
                    
                    if (data.slugChanges && data.slugChanges.length > 0) {
                        log(`🔄 Slug changes: ${data.slugChanges.length}`);
                        log('<h3>📝 Slug Changes:</h3>');
                        data.slugChanges.forEach(change => {
                            log(`<div class="slug-change">
                                <strong>${change.title}</strong><br>
                                Product ID: ${change.productId}<br>
                                Old: <span class="slug-old">${change.oldSlug}</span> (${change.oldLength} chars)<br>
                                New: <span class="slug-new">${change.newSlug}</span> (${change.newLength} chars)
                            </div>`);
                        });
                    } else {
                        log('✅ No slug changes needed - all slugs were already correct');
                    }
                } else {
                    log(`❌ Resync failed: ${result.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            } finally {
                resyncBtn.disabled = false;
                resyncBtn.textContent = '🚀 Resync All Courses';
            }
        }

        // Auto-run status check on page load
        window.addEventListener('load', () => {
            setTimeout(checkStatus, 1000);
        });
    </script>
</body>
</html>
