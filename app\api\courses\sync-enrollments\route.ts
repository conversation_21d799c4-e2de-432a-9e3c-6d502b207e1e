import { NextRequest } from 'next/server'
import { APIResponse, withAPIMiddleware } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { fetchGraphyLearnerEnrollments, GraphyApiError } from '@/lib/graphy-api'

// POST /api/courses/sync-enrollments - Sync user enrollments from Graphy
export const POST = withAPIMiddleware(
  async (request: NextRequest, { user }) => {
    try {
      // Get user details
      const userDetails = await prisma.user.findUnique({
        where: { id: user.id },
        select: { email: true, name: true }
      })

      if (!userDetails?.email) {
        return APIResponse.error('User email not found', 400)
      }

      console.log('🔄 Syncing enrollments from Graphy for:', userDetails.email)

      try {
        // Fetch enrolled courses from Graphy
        const graphyEnrollments = await fetchGraphyLearnerEnrollments(userDetails.email)
        console.log('📚 Found Graphy enrollments:', graphyEnrollments.length)

        let syncedCount = 0
        let updatedCount = 0
        let createdCount = 0

        for (const enrollment of graphyEnrollments) {
          // Find the course in our database
          const course = await prisma.course.findFirst({
            where: { productId: enrollment.productId }
          })

          if (course) {
            // Update or create enrollment record
            const result = await prisma.courseEnrollment.upsert({
              where: {
                userId_courseId: {
                  userId: user.id,
                  courseId: course.id
                }
              },
              update: {
                progress: enrollment.progress || 0,
                status: enrollment.status === 'completed' ? 'COMPLETED' : 'ACTIVE',
                lastAccessedAt: new Date()
              },
              create: {
                userId: user.id,
                courseId: course.id,
                productId: enrollment.productId,
                enrolledAt: new Date(enrollment.enrolledAt),
                progress: enrollment.progress || 0,
                status: enrollment.status === 'completed' ? 'COMPLETED' : 'ACTIVE'
              }
            })

            // Check if this was an update or create
            const existingEnrollment = await prisma.courseEnrollment.findFirst({
              where: {
                userId: user.id,
                courseId: course.id,
                createdAt: { lt: new Date(Date.now() - 1000) } // Created more than 1 second ago
              }
            })

            if (existingEnrollment) {
              updatedCount++
            } else {
              createdCount++
            }

            syncedCount++
          } else {
            console.warn(`⚠️ Course not found for productId: ${enrollment.productId}`)
          }
        }

        return APIResponse.success({
          message: 'Enrollments synced successfully',
          summary: {
            totalGraphyEnrollments: graphyEnrollments.length,
            syncedEnrollments: syncedCount,
            createdEnrollments: createdCount,
            updatedEnrollments: updatedCount
          },
          enrollments: graphyEnrollments.map(e => ({
            productId: e.productId,
            title: e.title,
            status: e.status,
            progress: e.progress,
            enrolledAt: e.enrolledAt
          }))
        })

      } catch (graphyError: any) {
        console.error('❌ Error fetching from Graphy:', graphyError)
        
        if (graphyError instanceof GraphyApiError) {
          return APIResponse.error(
            'Failed to sync with Graphy: ' + graphyError.message,
            500
          )
        }
        
        return APIResponse.error(
          'Failed to sync enrollments from Graphy',
          500
        )
      }

    } catch (error: any) {
      console.error('❌ Error syncing enrollments:', error)
      return APIResponse.error(
        'Failed to sync enrollments: ' + error.message,
        500
      )
    }
  },
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  }
)

// GET /api/courses/sync-enrollments - Check sync status
export const GET = withAPIMiddleware(
  async (request: NextRequest, { user }) => {
    try {
      // Get user's current enrollments
      const enrollments = await prisma.courseEnrollment.findMany({
        where: { userId: user.id },
        include: {
          course: {
            select: {
              title: true,
              productId: true,
              slug: true
            }
          }
        },
        orderBy: { enrolledAt: 'desc' }
      })

      const summary = {
        totalEnrollments: enrollments.length,
        activeEnrollments: enrollments.filter(e => e.status === 'ACTIVE').length,
        completedEnrollments: enrollments.filter(e => e.status === 'COMPLETED').length,
        pendingEnrollments: enrollments.filter(e => e.status === 'PENDING').length
      }

      return APIResponse.success({
        message: 'Current enrollment status',
        summary,
        enrollments: enrollments.map(e => ({
          id: e.id,
          courseTitle: e.course.title,
          productId: e.course.productId,
          status: e.status,
          progress: e.progress,
          enrolledAt: e.enrolledAt,
          lastAccessedAt: e.lastAccessedAt
        }))
      })

    } catch (error: any) {
      console.error('❌ Error checking sync status:', error)
      return APIResponse.error(
        'Failed to check sync status: ' + error.message,
        500
      )
    }
  },
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  }
)
